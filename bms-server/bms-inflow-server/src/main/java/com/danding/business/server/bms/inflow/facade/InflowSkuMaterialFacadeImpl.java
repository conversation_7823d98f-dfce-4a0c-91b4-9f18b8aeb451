package com.danding.business.server.bms.inflow.facade;

import com.danding.business.client.bms.inflow.facade.IInflowSkuMaterialFacade;
import com.danding.business.server.bms.inflow.manager.InflowSkuMaterialManager;
import com.danding.business.client.bms.inflow.result.InflowSkuMaterialResult;
import com.danding.business.client.bms.inflow.param.InflowSkuMaterialQueryParam;
import com.danding.business.client.bms.inflow.param.InflowSkuMaterialAddParam;
import com.danding.business.core.bms.inflow.search.InflowSkuMaterialSearch;
import com.danding.business.server.bms.inflow.BO.InflowSkuMaterialBO;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.DubboService;


import java.io.Serializable;
import java.util.List;


/**
 * <p>
 * 货主 商品 耗材对应关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-08
 */
@DubboService
public class InflowSkuMaterialFacadeImpl implements IInflowSkuMaterialFacade {

    @Autowired
    private InflowSkuMaterialManager inflowSkuMaterialManager;

    /**
     * 主键查询
     *
     * @param id
     * @return
     */
    @Override
    public InflowSkuMaterialResult getById(Serializable id) {
        InflowSkuMaterialBO inflowSkuMaterialBO = inflowSkuMaterialManager.getById(id);
        return BeanUtils.copyProperties(inflowSkuMaterialBO, InflowSkuMaterialResult.class);
    }

    /**
     * 条件查询单个
     *
     * @param inflowSkuMaterialQueryParam
     * @return
     */
    @Override
    public InflowSkuMaterialResult getByQueryParam(InflowSkuMaterialQueryParam inflowSkuMaterialQueryParam) {
        InflowSkuMaterialBO inflowSkuMaterialBO = inflowSkuMaterialManager.getBySearch(BeanUtils.copyProperties(inflowSkuMaterialQueryParam, InflowSkuMaterialSearch.class));
        return BeanUtils.copyProperties(inflowSkuMaterialBO, InflowSkuMaterialResult.class);
    }

    /**
     * 条件查询list
     *
     * @param inflowSkuMaterialQueryParam
     * @return
     */
    @Override
    public List<InflowSkuMaterialResult> listByQueryParam(InflowSkuMaterialQueryParam inflowSkuMaterialQueryParam) {
        List<InflowSkuMaterialBO> inflowSkuMaterialBOList = inflowSkuMaterialManager.listBySearch(BeanUtils.copyProperties(inflowSkuMaterialQueryParam, InflowSkuMaterialSearch.class));
        return BeanUtils.copyProperties(inflowSkuMaterialBOList, InflowSkuMaterialResult.class);
    }

    /**
     * 条件分页查询
     *
     * @param inflowSkuMaterialQueryParam
     * @return
     */
    @Override
    public ListVO<InflowSkuMaterialResult> pageListByQueryParam(InflowSkuMaterialQueryParam inflowSkuMaterialQueryParam) {
        ListVO<InflowSkuMaterialBO> inflowSkuMaterialBOListVO = inflowSkuMaterialManager.pageListBySearch(BeanUtils.copyProperties(inflowSkuMaterialQueryParam, InflowSkuMaterialSearch.class));
        return ListVO.build(inflowSkuMaterialBOListVO.getPage(), BeanUtils.copyProperties(inflowSkuMaterialBOListVO.getDataList(), InflowSkuMaterialResult.class));
    }

    /**
     * 插入
     *
     * @param inflowSkuMaterialAddParam
     * @return
     */
    @Override
    public boolean add(InflowSkuMaterialAddParam inflowSkuMaterialAddParam) {
        return inflowSkuMaterialManager.add(BeanUtils.copyProperties(inflowSkuMaterialAddParam, InflowSkuMaterialBO.class));
    }

    /**
     * 批量插入
     *
     * @param inflowSkuMaterialAddParamList
     * @return
     */
    @Override
    public boolean addList(List<InflowSkuMaterialAddParam> inflowSkuMaterialAddParamList) {
        return inflowSkuMaterialManager.addList(BeanUtils.copyProperties(inflowSkuMaterialAddParamList, InflowSkuMaterialBO.class));
    }

    /**
     * 根据主键id修改
     *
     * @param inflowSkuMaterialAddParam
     * @return
     */
    @Override
    public boolean updateById(InflowSkuMaterialAddParam inflowSkuMaterialAddParam) {
        return inflowSkuMaterialManager.updateById(BeanUtils.copyProperties(inflowSkuMaterialAddParam, InflowSkuMaterialBO.class));
    }

    /**
     * 根据主键id批量修改
     *
     * @param inflowSkuMaterialAddParamList
     * @return
     */
    @Override
    public boolean updateListById(List<InflowSkuMaterialAddParam> inflowSkuMaterialAddParamList) {
        return inflowSkuMaterialManager.updateListById(BeanUtils.copyProperties(inflowSkuMaterialAddParamList, InflowSkuMaterialBO.class));
    }

    /**
     * 根据条件修改
     *
     * @param inflowSkuMaterialQueryParam
     * @param inflowSkuMaterialAddParam
     * @return
     */
    @Override
    public boolean updateListByQueryParam(InflowSkuMaterialQueryParam inflowSkuMaterialQueryParam, InflowSkuMaterialAddParam inflowSkuMaterialAddParam) {
        return inflowSkuMaterialManager.updateListBySearch(BeanUtils.copyProperties(inflowSkuMaterialQueryParam, InflowSkuMaterialSearch.class), BeanUtils.copyProperties(inflowSkuMaterialAddParam, InflowSkuMaterialBO.class));
    }

    /**
     * 根据主键id删除
     *
     * @param id
     * @return
     */
    @Override
    public boolean removeById(Serializable id) {
        return inflowSkuMaterialManager.removeById(id);
    }

    /**
     * 根据主键id批量删除
     *
     * @param idList
     * @return
     */
    @Override
    public boolean removeByIds(List<Long> idList) {
        return inflowSkuMaterialManager.removeByIds(idList);
    }

    /**
     * 根据条件删除
     *
     * @param inflowSkuMaterialQueryParam
     * @return
     */
    @Override
    public boolean removeByQueryParam(InflowSkuMaterialQueryParam inflowSkuMaterialQueryParam) {
        return inflowSkuMaterialManager.removeBySearch(BeanUtils.copyProperties(inflowSkuMaterialQueryParam, InflowSkuMaterialSearch.class));
    }

}
