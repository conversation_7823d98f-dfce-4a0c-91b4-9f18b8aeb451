package com.danding.business.server.bms.inflow.facade;

import com.danding.business.client.bms.inflow.facade.IInflowAsnDetailFacade;
import com.danding.business.server.bms.inflow.manager.InflowAsnDetailManager;
import com.danding.business.client.bms.inflow.result.InflowAsnDetailResult;
import com.danding.business.client.bms.inflow.param.InflowAsnDetailQueryParam;
import com.danding.business.client.bms.inflow.param.InflowAsnDetailAddParam;
import com.danding.business.core.bms.inflow.search.InflowAsnDetailSearch;
import com.danding.business.server.bms.inflow.BO.InflowAsnDetailBO;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.DubboService;


import java.io.Serializable;
import java.util.List;


/**
 * <p>
 * 到货通知明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-16
 */
@DubboService
public class InflowAsnDetailFacadeImpl implements IInflowAsnDetailFacade {

    @Autowired
    private InflowAsnDetailManager inflowAsnDetailManager;

    /**
     * 主键查询
     *
     * @param id
     * @return
     */
    @Override
    public InflowAsnDetailResult getById(Serializable id) {
        InflowAsnDetailBO inflowAsnDetailBO = inflowAsnDetailManager.getById(id);
        return BeanUtils.copyProperties(inflowAsnDetailBO, InflowAsnDetailResult.class);
    }

    /**
     * 条件查询单个
     *
     * @param inflowAsnDetailQueryParam
     * @return
     */
    @Override
    public InflowAsnDetailResult getByQueryParam(InflowAsnDetailQueryParam inflowAsnDetailQueryParam) {
        InflowAsnDetailBO inflowAsnDetailBO = inflowAsnDetailManager.getBySearch(BeanUtils.copyProperties(inflowAsnDetailQueryParam, InflowAsnDetailSearch.class));
        return BeanUtils.copyProperties(inflowAsnDetailBO, InflowAsnDetailResult.class);
    }

    /**
     * 条件查询list
     *
     * @param inflowAsnDetailQueryParam
     * @return
     */
    @Override
    public List<InflowAsnDetailResult> listByQueryParam(InflowAsnDetailQueryParam inflowAsnDetailQueryParam) {
        List<InflowAsnDetailBO> inflowAsnDetailBOList = inflowAsnDetailManager.listBySearch(BeanUtils.copyProperties(inflowAsnDetailQueryParam, InflowAsnDetailSearch.class));
        return BeanUtils.copyProperties(inflowAsnDetailBOList, InflowAsnDetailResult.class);
    }

    /**
     * 条件分页查询
     *
     * @param inflowAsnDetailQueryParam
     * @return
     */
    @Override
    public ListVO<InflowAsnDetailResult> pageListByQueryParam(InflowAsnDetailQueryParam inflowAsnDetailQueryParam) {
        ListVO<InflowAsnDetailBO> inflowAsnDetailBOListVO = inflowAsnDetailManager.pageListBySearch(BeanUtils.copyProperties(inflowAsnDetailQueryParam, InflowAsnDetailSearch.class));
        return ListVO.build(inflowAsnDetailBOListVO.getPage(), BeanUtils.copyProperties(inflowAsnDetailBOListVO.getDataList(), InflowAsnDetailResult.class));
    }

    /**
     * 插入
     *
     * @param inflowAsnDetailAddParam
     * @return
     */
    @Override
    public boolean add(InflowAsnDetailAddParam inflowAsnDetailAddParam) {
        return inflowAsnDetailManager.add(BeanUtils.copyProperties(inflowAsnDetailAddParam, InflowAsnDetailBO.class));
    }

    /**
     * 批量插入
     *
     * @param inflowAsnDetailAddParamList
     * @return
     */
    @Override
    public boolean addList(List<InflowAsnDetailAddParam> inflowAsnDetailAddParamList) {
        return inflowAsnDetailManager.addList(BeanUtils.copyProperties(inflowAsnDetailAddParamList, InflowAsnDetailBO.class));
    }

    /**
     * 根据主键id修改
     *
     * @param inflowAsnDetailAddParam
     * @return
     */
    @Override
    public boolean updateById(InflowAsnDetailAddParam inflowAsnDetailAddParam) {
        return inflowAsnDetailManager.updateById(BeanUtils.copyProperties(inflowAsnDetailAddParam, InflowAsnDetailBO.class));
    }

    /**
     * 根据主键id批量修改
     *
     * @param inflowAsnDetailAddParamList
     * @return
     */
    @Override
    public boolean updateListById(List<InflowAsnDetailAddParam> inflowAsnDetailAddParamList) {
        return inflowAsnDetailManager.updateListById(BeanUtils.copyProperties(inflowAsnDetailAddParamList, InflowAsnDetailBO.class));
    }

    /**
     * 根据条件修改
     *
     * @param inflowAsnDetailQueryParam
     * @param inflowAsnDetailAddParam
     * @return
     */
    @Override
    public boolean updateListByQueryParam(InflowAsnDetailQueryParam inflowAsnDetailQueryParam, InflowAsnDetailAddParam inflowAsnDetailAddParam) {
        return inflowAsnDetailManager.updateListBySearch(BeanUtils.copyProperties(inflowAsnDetailQueryParam, InflowAsnDetailSearch.class), BeanUtils.copyProperties(inflowAsnDetailAddParam, InflowAsnDetailBO.class));
    }

    /**
     * 根据主键id删除
     *
     * @param id
     * @return
     */
    @Override
    public boolean removeById(Serializable id) {
        return inflowAsnDetailManager.removeById(id);
    }

    /**
     * 根据主键id批量删除
     *
     * @param idList
     * @return
     */
    @Override
    public boolean removeByIds(List<Long> idList) {
        return inflowAsnDetailManager.removeByIds(idList);
    }

    /**
     * 根据条件删除
     *
     * @param inflowAsnDetailQueryParam
     * @return
     */
    @Override
    public boolean removeByQueryParam(InflowAsnDetailQueryParam inflowAsnDetailQueryParam) {
        return inflowAsnDetailManager.removeBySearch(BeanUtils.copyProperties(inflowAsnDetailQueryParam, InflowAsnDetailSearch.class));
    }

}
