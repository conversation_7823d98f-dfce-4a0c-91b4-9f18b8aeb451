package com.danding.business.job.fee.handle;

import com.danding.business.client.bms.inflow.result.InflowWarehouseResult;
import com.danding.business.job.fee.base.CallableProcess;
import com.danding.business.job.fee.handleservice.ImportsCarrierService;
import com.danding.business.job.fee.handleservice.ImportsEntityWareHouseService;
import com.danding.business.job.fee.util.SpringBeanUtil;
import org.springframework.beans.factory.annotation.Autowired;


public class ImportsEntityWareHouseHandle extends CallableProcess<Boolean>{

    @Autowired
    private ImportsEntityWareHouseService importsWareHouseService;

    private InflowWarehouseResult dictResult;
    private String monthDaySelect;
    private String month;
    private String monthDayDate;
    private Long monthDayAddDate;
    public ImportsEntityWareHouseHandle(InflowWarehouseResult dictResult, String monthDaySelect, String month, String monthDayDate,Long monthDayAddDate) {
        this.dictResult = dictResult;
        this.monthDaySelect=monthDaySelect;
        this.month=month;
        this.monthDayDate=monthDayDate;
        this.importsWareHouseService= SpringBeanUtil.getBean(ImportsEntityWareHouseService.class);
    }

    @Override
    public Boolean process() {
        return importsWareHouseService.SyncImportsData(dictResult,monthDaySelect,month,monthDayDate,monthDayAddDate);
    }
}
