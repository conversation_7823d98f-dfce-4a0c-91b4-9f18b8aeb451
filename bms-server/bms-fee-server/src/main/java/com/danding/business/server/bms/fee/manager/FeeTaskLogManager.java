package com.danding.business.server.bms.fee.manager;

import com.danding.business.core.bms.fee.service.IFeeTaskLogService;
import com.danding.business.server.bms.fee.manager.helper.FeeTaskLogManagerHelper;
import com.danding.business.server.bms.fee.BO.FeeTaskLogBO;
import com.danding.business.core.bms.fee.search.FeeTaskLogSearch;
import com.danding.component.boost.annotation.PageSelect;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.business.core.bms.fee.entity.FeeTaskLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.List;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;

/**
 * <p>
 * 计费进度日志表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-05
 */
@Component
public class FeeTaskLogManager {

    @Autowired
    private IFeeTaskLogService feetasklogService;

    @Autowired
    private FeeTaskLogManagerHelper feeTaskLogManagerHelper;


    /**
    * 列表查询查询
    *
    * @param search
    * @return
    */
    public List<FeeTaskLogBO> listFeeTaskLogByParam(FeeTaskLogSearch search) {
    List<FeeTaskLog> feetasklog = feetasklogService.selectFeeTaskLogListByParam(search);
        if (CollectionUtils.isNotEmpty(feetasklog)) {
        return BeanUtils.copyProperties(feetasklog, FeeTaskLogBO.class);
        }
        return null;
    }

    /**
    * 分页查询查询
    *
    * @param search
    * @return
    */
    @PageSelect
    public ListVO<FeeTaskLogBO> pageListFeeTaskLogByParam(FeeTaskLogSearch search) {
        List<FeeTaskLog> feetasklog = feetasklogService.selectFeeTaskLogListByParam(search);
        ListVO<FeeTaskLogBO> listVO = new ListVO<>();
        List<FeeTaskLogBO> bos = BeanUtils.copyProperties(feetasklog, FeeTaskLogBO.class);
        listVO.setDataList(bos);
        return listVO;
    }


    /**
     * id查询单个
     *
     * @param id
     * @return
     */
    public FeeTaskLogBO getFeeTaskLogBOById(Serializable id) {
        return BeanUtils.copyProperties(feetasklogService.selectById(id), FeeTaskLogBO.class);
    }

    /**
     * 条件查询单个
     *
     * @param feeTaskLogSearch
     * @return
     */
    public FeeTaskLogBO getFeeTaskLogBOByFeeTaskLogSearch(FeeTaskLogSearch feeTaskLogSearch) {
        FeeTaskLog feeTaskLog = feetasklogService.selectByFeeTaskLogSearch(feeTaskLogSearch);
        return BeanUtils.copyProperties(feeTaskLog, FeeTaskLogBO.class);
    }

    /**
     * 列表查询
     *
     * @param feeTaskLogSearch
     * @return
     */
    public List<FeeTaskLogBO> listFeeTaskLogBOByFeeTaskLogSearch(FeeTaskLogSearch feeTaskLogSearch) {
        List<FeeTaskLog> feeTaskLogList = feetasklogService.selectListByFeeTaskLogSearch(feeTaskLogSearch);
        return BeanUtils.copyProperties(feeTaskLogList, FeeTaskLogBO.class);
    }

    /**
     * 分页查询
     *
     * @param feeTaskLogSearch
     * @return
     */
    @PageSelect
    public ListVO<FeeTaskLogBO> pageListFeeTaskLogBOByFeeTaskLogSearch(FeeTaskLogSearch feeTaskLogSearch) {
        ListVO<FeeTaskLogBO> feeTaskLogBOListVO = new ListVO<>();
        List<FeeTaskLog> feeTaskLogList = feetasklogService.selectListByFeeTaskLogSearch(feeTaskLogSearch);
        return ListVO.build(feeTaskLogBOListVO.getPage(), BeanUtils.copyProperties(feeTaskLogList, FeeTaskLogBO.class));
    }

    /**
     * 功能描述:  插入
     */
    public boolean addFeeTaskLog(FeeTaskLogBO feeTaskLogBO) {
        return feetasklogService.insert(BeanUtils.copyProperties(feeTaskLogBO, FeeTaskLog.class));
    }

    /**
     * 功能描述:  批量插入
     */
    public boolean addFeeTaskLogList(List<FeeTaskLogBO> feeTaskLogBOList) {
        return feetasklogService.insertList(BeanUtils.copyProperties(feeTaskLogBOList, FeeTaskLog.class));
    }

    /**
     * 功能描述:  根据主键id修改
     */
    public boolean updateFeeTaskLogById(FeeTaskLogBO feeTaskLogBO) {
        return feetasklogService.updateById(BeanUtils.copyProperties(feeTaskLogBO, FeeTaskLog.class));
    }

    /**
     * 功能描述:  根据主键id批量修改
     */
    public boolean updateFeeTaskLogListById(List<FeeTaskLogBO> feeTaskLogBOList) {
        return feetasklogService.updateListById(BeanUtils.copyProperties(feeTaskLogBOList, FeeTaskLog.class));
    }

    /**
     * 功能描述:  根据条件修改
     */
    public boolean updateFeeTaskLogListByFeeTaskLogSearch(FeeTaskLogSearch feeTaskLogSearch, FeeTaskLogBO feeTaskLogBO) {
        return feetasklogService.updateListByFeeTaskLogSearch(feeTaskLogSearch, BeanUtils.copyProperties(feeTaskLogBO, FeeTaskLog.class));
    }

    /**
     * 功能描述:  根据主键id删除
     */
    public boolean removeFeeTaskLogByIds(Serializable id) {
        return feetasklogService.deleteById(id);
    }

    /**
     * 功能描述:  根据主键id批量删除
     */
    public boolean removeFeeTaskLogByIds(List<Long> idList) {
        return feetasklogService.deleteByIds(idList);
    }

    /**
     * 功能描述:  根据条件删除
     */
    public boolean removeFeeTaskLogByFeeTaskLogSearch(FeeTaskLogSearch feeTaskLogSearch) {
        return feetasklogService.deleteByFeeTaskLogSearch(feeTaskLogSearch);
    }

}
