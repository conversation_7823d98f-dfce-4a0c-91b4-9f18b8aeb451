package com.danding.business.server.bms.fee.manager;

import com.danding.business.core.bms.fee.search.FeeOtherListSearch;
import com.danding.business.core.bms.fee.search.FeeTobListSearch;
import com.danding.business.core.bms.fee.service.IFeeTobService;
import com.danding.business.server.bms.fee.manager.helper.FeeTobManagerHelper;
import com.danding.business.server.bms.fee.BO.FeeTobBO;
import com.danding.business.core.bms.fee.search.FeeTobSearch;
import com.danding.component.boost.annotation.PageSelect;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.business.core.bms.fee.entity.FeeTob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.List;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;

/**
 * <p>
 * 2B费用表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-05
 */
@Component
public class FeeTobManager {

    @Autowired
    private IFeeTobService feetobService;

    @Autowired
    private FeeTobManagerHelper feeTobManagerHelper;



    /**
     * 查询2B的列表
     *
     * @param search
     * @return
     */
    @PageSelect
    public List<FeeTobListSearch> SelectTobFeeList(FeeTobSearch search){
        return feetobService.SelectTobFeeList(search);
    }

    /**
     * 列表查询查询
     *
     * @param search
     * @return
     */
    public List<FeeTobBO> listFeeJobTobByParam(FeeTobSearch search) {
        List<FeeTob> feetob = feetobService.selectFeeJobTobListByParam(search);
        if (CollectionUtils.isNotEmpty(feetob)) {
            return BeanUtils.copyProperties(feetob, FeeTobBO.class);
        }
        return null;
    }


    /**
    * 列表查询查询
    *
    * @param search
    * @return
    */
    public List<FeeTobBO> listFeeTobByParam(FeeTobSearch search) {
    List<FeeTob> feetob = feetobService.selectFeeTobListByParam(search);
        if (CollectionUtils.isNotEmpty(feetob)) {
        return BeanUtils.copyProperties(feetob, FeeTobBO.class);
        }
        return null;
    }

    /**
    * 分页查询查询
    *
    * @param search
    * @return
    */
    @PageSelect
    public ListVO<FeeTobBO> pageListFeeTobByParam(FeeTobSearch search) {
        List<FeeTob> feetob = feetobService.selectFeeTobListByParam(search);
        ListVO<FeeTobBO> listVO = new ListVO<>();
        List<FeeTobBO> bos = BeanUtils.copyProperties(feetob, FeeTobBO.class);
        listVO.setDataList(bos);
        return listVO;
    }


    /**
     * id查询单个
     *
     * @param id
     * @return
     */
    public FeeTobBO getFeeTobBOById(Serializable id) {
        return BeanUtils.copyProperties(feetobService.selectById(id), FeeTobBO.class);
    }

    /**
     * 条件查询单个
     *
     * @param feeTobSearch
     * @return
     */
    public FeeTobBO getFeeTobBOByFeeTobSearch(FeeTobSearch feeTobSearch) {
        FeeTob feeTob = feetobService.selectByFeeTobSearch(feeTobSearch);
        return BeanUtils.copyProperties(feeTob, FeeTobBO.class);
    }

    /**
     * 列表查询
     *
     * @param feeTobSearch
     * @return
     */
    public List<FeeTobBO> listFeeTobBOByFeeTobSearch(FeeTobSearch feeTobSearch) {
        List<FeeTob> feeTobList = feetobService.selectListByFeeTobSearch(feeTobSearch);
        return BeanUtils.copyProperties(feeTobList, FeeTobBO.class);
    }

    /**
     * 分页查询
     *
     * @param feeTobSearch
     * @return
     */
    @PageSelect
    public ListVO<FeeTobBO> pageListFeeTobBOByFeeTobSearch(FeeTobSearch feeTobSearch) {
        ListVO<FeeTobBO> feeTobBOListVO = new ListVO<>();
        List<FeeTob> feeTobList = feetobService.selectListByFeeTobSearch(feeTobSearch);
        return ListVO.build(feeTobBOListVO.getPage(), BeanUtils.copyProperties(feeTobList, FeeTobBO.class));
    }

    /**
     * 功能描述:  插入
     */
    public boolean addFeeTob(FeeTobBO feeTobBO) {
        return feetobService.insert(BeanUtils.copyProperties(feeTobBO, FeeTob.class));
    }

    /**
     * 功能描述:  批量插入
     */
    public boolean addFeeTobList(List<FeeTobBO> feeTobBOList) {
        return feetobService.insertList(BeanUtils.copyProperties(feeTobBOList, FeeTob.class));
    }

    /**
     * 功能描述:  根据主键id修改
     */
    public boolean updateFeeTobById(FeeTobBO feeTobBO) {
        return feetobService.updateById(BeanUtils.copyProperties(feeTobBO, FeeTob.class));
    }

    /**
     * 功能描述:  根据主键id批量修改
     */
    public boolean updateFeeTobListById(List<FeeTobBO> feeTobBOList) {
        return feetobService.updateListById(BeanUtils.copyProperties(feeTobBOList, FeeTob.class));
    }

    /**
     * 功能描述:  根据条件修改
     */
    public boolean updateFeeTobListByFeeTobSearch(FeeTobSearch feeTobSearch, FeeTobBO feeTobBO) {
        return feetobService.updateListByFeeTobSearch(feeTobSearch, BeanUtils.copyProperties(feeTobBO, FeeTob.class));
    }

    /**
     * 功能描述:  根据主键id删除
     */
    public boolean removeFeeTobByIds(Serializable id) {
        return feetobService.deleteById(id);
    }

    /**
     * 功能描述:  根据主键id批量删除
     */
    public boolean removeFeeTobByIds(List<Long> idList) {
        return feetobService.deleteByIds(idList);
    }

    /**
     * 功能描述:  根据条件删除
     */
    public boolean removeFeeTobByFeeTobSearch(FeeTobSearch feeTobSearch) {
        return feetobService.deleteByFeeTobSearch(feeTobSearch);
    }

}
