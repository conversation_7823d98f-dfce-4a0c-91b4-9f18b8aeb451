package com.danding.business.server.bms.fee.facade.export;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.danding.business.client.bms.fee.facade.IFeeStorageSetFacade;
import com.danding.business.client.bms.fee.facade.IFeeTotalDetailFacade;
import com.danding.business.client.bms.fee.param.FeeStorageSetQueryParam;
import com.danding.business.client.bms.fee.param.FeeTotalDetailQueryParam;
import com.danding.business.client.bms.fee.result.FeeStorageSetResult;
import com.danding.business.client.bms.fee.result.FeeTotalDetailResult;
import com.danding.business.client.bms.inflow.facade.IInflowShelfDetailFacade;
import com.danding.business.client.bms.inflow.facade.IInflowShipmentOrderFacade;
import com.danding.business.client.bms.inflow.facade.IInflowSkuFacade;
import com.danding.business.client.bms.inflow.facade.IInflowStockFacade;
import com.danding.business.client.bms.inflow.param.InflowSkuQueryParam;
import com.danding.business.client.bms.inflow.param.InflowStockQueryParam;
import com.danding.business.client.bms.inflow.result.InflowShelfDetailResult;
import com.danding.business.client.bms.inflow.result.InflowShipmentOrderResult;
import com.danding.business.client.bms.inflow.result.InflowSkuResult;
import com.danding.business.client.bms.inflow.result.InflowStockResult;
import com.danding.business.common.bms.emums.ExportTypeEnum;
import com.danding.business.common.bms.excel.CustomCellWriteHandler;
import com.danding.business.common.bms.utils.DateUtil;
import com.danding.business.common.bms.vo.FeeTotalDetailVO;
import com.danding.business.server.bms.fee.excel.FeeStorageExportVO;
import com.danding.business.server.bms.fee.facade.totalDetailStrategy.BaseExportService;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.api.common.response.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class FeeStorageOtherTempServiceImpl  extends BaseExportService<FeeStorageExportVO> {
    @Resource
    private IFeeTotalDetailFacade feetotaldetailFacade;
    @Resource
    private IFeeStorageSetFacade feestoragesetFacade;
    @DubboReference
    private IInflowStockFacade iInflowStockFacade;
    @DubboReference
    private IInflowSkuFacade iInflowSkuFacade;
    @DubboReference
    IInflowShelfDetailFacade iInflowShelfDetailFacade;
    @DubboReference
    private IInflowShipmentOrderFacade inflowshipmentorderFacade;

    @Override
    public List<FeeStorageExportVO> selectListForExcelExport(FeeTotalDetailVO feeTotalDetailVO, int page , int pageSize){
        List<FeeStorageExportVO> listFeeCommonExportVO=new ArrayList<>();
        FeeTotalDetailResult feeTotalDetail = feetotaldetailFacade.getFeeTotalDetailById(feeTotalDetailVO.getId());
        if (feeTotalDetail != null) {
            //仓储费
            FeeStorageSetQueryParam queryParam = new FeeStorageSetQueryParam();
            queryParam.setContractFeeId(feeTotalDetailVO.getContractFeeId());
            queryParam.setFeeClass("仓储费");
            FeeStorageSetResult feeStorageSetResult = feestoragesetFacade.getFeeStorageSetByFeeStorageSetQueryParam(queryParam);

            InflowStockQueryParam inflowStockQueryParam = new InflowStockQueryParam();
            inflowStockQueryParam.setWarehouseCode(feeTotalDetailVO.getWarehouseCode());
            inflowStockQueryParam.setCargoCode(feeTotalDetailVO.getCargoCode());
            inflowStockQueryParam.setImportMonth(feeTotalDetail.getFeeCycle());
            inflowStockQueryParam.setImportDateStart(feeStorageSetResult.getHwStartDate());
            inflowStockQueryParam.setImportDateEnd(feeStorageSetResult.getHwEndDate());
            inflowStockQueryParam.setCurrentPage(page);
            inflowStockQueryParam.setPageSize(pageSize);
            List<InflowStockResult> listInflowStockResult = iInflowStockFacade.pageListInflowStockByInflowStockQueryParam(inflowStockQueryParam).getDataList();

            listInflowStockResult = listInflowStockResult.stream().filter(s->s.getTempQty()!=null &&
                            s.getTempQty().compareTo(BigDecimal.ZERO)>0)
                    .collect(Collectors.toList());

            List<InflowShelfDetailResult>  listInflowShelfDetailResult=iInflowShelfDetailFacade.SelectMinReceiveDate(feeTotalDetailVO.getCargoCode(),feeTotalDetailVO.getWarehouseCode());
            List<InflowShelfDetailResult> listInflowShelfDetailNewResult=new ArrayList<>();
            if(listInflowShelfDetailResult!=null && listInflowShelfDetailResult.size()>0) {
                listInflowShelfDetailNewResult=listInflowShelfDetailResult.stream()
                        .sorted(Comparator.comparing(InflowShelfDetailResult::getCreateTime,Comparator.nullsLast(Long::compareTo))).collect(Collectors.toList());
            }
            String dateFirstWarehousing="";
            if(listInflowShelfDetailNewResult!=null && listInflowShelfDetailNewResult.size()>0){
                dateFirstWarehousing=(DateUtil.getDateForLong(listInflowShelfDetailNewResult.get(0).getCreateTime()));
            }
            List<InflowSkuResult> listInflowSkuResult;
            int currentPage=1;
            int size=3000;
            int totalPage;
            InflowSkuQueryParam queryParamSku=new InflowSkuQueryParam();
            queryParamSku.setWarehouseCode(feeTotalDetailVO.getWarehouseCode());
            queryParamSku.setCargoCode(feeTotalDetailVO.getCargoCode());
            queryParamSku.setRealDeleted(true);
            queryParamSku.setCurrentPage(currentPage);
            queryParamSku.setPageSize(size);
            ListVO<InflowSkuResult> listVOInflowSkuResult=iInflowSkuFacade.pageListInflowSkuByInflowSkuQueryParam(queryParamSku);
            listInflowSkuResult=listVOInflowSkuResult.getDataList();
            PageResult pageResult =listVOInflowSkuResult.getPage();
            totalPage=pageResult.getTotalPage();
            for (currentPage=1;currentPage<=totalPage;currentPage++) {
                if(currentPage>1) {
                    queryParamSku.setCurrentPage(currentPage);
                    listVOInflowSkuResult=iInflowSkuFacade.pageListInflowSkuByInflowSkuQueryParam(queryParamSku);
                    List<InflowSkuResult> listNewInflowSkuResult=listVOInflowSkuResult.getDataList();
                    if(listNewInflowSkuResult!=null && listNewInflowSkuResult.size()>0) {
                        listInflowSkuResult.addAll(listNewInflowSkuResult);
                    }
                }
            }

            if (feeStorageSetResult!=null) {
                if(listInflowStockResult!=null && listInflowStockResult.size()>0) {
                    for (InflowStockResult inflowStockResult:listInflowStockResult) {
                        if(inflowStockResult.getTempQty().compareTo(BigDecimal.ZERO) == 0){
                            continue;
                        }
                        FeeStorageExportVO feeCommonExportVO1=new FeeStorageExportVO();
                        feeCommonExportVO1.setFeeDateTime(inflowStockResult.getImportMonthDay());
                        feeCommonExportVO1.setProductCode(inflowStockResult.getSkuCode());
                        feeCommonExportVO1.setSkuQuality(inflowStockResult.getSkuQuality().equals("AVL")?"正品":"次品");
                        feeCommonExportVO1.setVolume(inflowStockResult.getVolume());
                        if(listInflowSkuResult!=null && listInflowSkuResult.size()>0 && inflowStockResult.getSkuCode()!=null) {
                            if(listInflowSkuResult.stream().filter(f->f.getCode().equals(inflowStockResult.getSkuCode())).count()>0) {
                                InflowSkuResult inflowSkuResult=listInflowSkuResult.stream().filter(f->f.getCode().equals(inflowStockResult.getSkuCode())).findFirst().get();
                                if(inflowSkuResult!=null) {
                                    feeCommonExportVO1.setProductName(inflowSkuResult.getName());
                                    feeCommonExportVO1.setLength(inflowStockResult.getLength());
                                    feeCommonExportVO1.setWidth(inflowStockResult.getWidth());
                                    feeCommonExportVO1.setHeight(inflowStockResult.getHeight());
                                }
                            }
                        }
                        if(feeCommonExportVO1.getProductName()==null) {
                            feeCommonExportVO1.setProductName("");
                        }
                        feeCommonExportVO1.setOutStockQty(inflowStockResult.getTempQty());

                        feeCommonExportVO1.setGrossWeight(inflowStockResult.getGrossWeight());
                        listFeeCommonExportVO.add(feeCommonExportVO1);
                    }
                    if(CollectionUtil.isNotEmpty(listFeeCommonExportVO) && page == 1){
                        //C单月份订单总量
                        List<InflowShipmentOrderResult> listInflowShipmentOrderResult=
                                inflowshipmentorderFacade.SelectCommonCountList(feeTotalDetailVO.getWarehouseCode(),feeTotalDetailVO.getCargoCode(),
                                        null,feeTotalDetail.getFeeCycle(),null,"B2C");
                        if(listInflowShipmentOrderResult!=null && listInflowShipmentOrderResult.size()>0) {
                            listFeeCommonExportVO.get(0).setListPageSize(listInflowShipmentOrderResult.get(0).getPackageQty());
                        }
                        listFeeCommonExportVO.get(0).setWarehouseName(feeTotalDetailVO.getWarehouseName());
                        listFeeCommonExportVO.get(0).setCargoName(feeTotalDetailVO.getCargoName());
                        listFeeCommonExportVO.get(0).setDateFirstWarehousing(dateFirstWarehousing);
                    }
                }
            }
        }
        return listFeeCommonExportVO;
    }

    @Override
    public List<FeeStorageExportVO> selectListForExcelExportByFeeTotalDetailList(List<FeeTotalDetailVO> feeTotalDetailVOList, int page , int pageSize) {
        List<FeeStorageExportVO> listFeeCommonExportVO=new ArrayList<>();
        List<String> idList = feeTotalDetailVOList.stream().map(f -> f.getId().toString()).collect(Collectors.toList());
        HashMap<String,String> warehouseMap = new HashMap<>();
        HashMap<String,String> cargoMap = new HashMap<>();

        for (FeeTotalDetailVO feeTotalDetailVO : feeTotalDetailVOList) {
            warehouseMap.computeIfAbsent(feeTotalDetailVO.getFeeTotalDetailResult().getWarehouseCode(), k -> feeTotalDetailVO.getWarehouseName());
            cargoMap.computeIfAbsent(feeTotalDetailVO.getFeeTotalDetailResult().getCargoCode(), k -> feeTotalDetailVO.getCargoName());
        }
        FeeTotalDetailQueryParam feeTotalDetailQueryParam = new FeeTotalDetailQueryParam();
        feeTotalDetailQueryParam.setIds(idList);
        List<FeeTotalDetailResult> feeTotalDetailResultList = feetotaldetailFacade.listFeeTotalDetailByFeeTotalDetailQueryParam(feeTotalDetailQueryParam);
        if (!CollectionUtils.isEmpty(feeTotalDetailResultList)) {
            InflowStockQueryParam inflowStockQueryParam = new InflowStockQueryParam();
            inflowStockQueryParam.setWarehouseCodeList(feeTotalDetailResultList.stream().map(FeeTotalDetailResult::getWarehouseCode).distinct().collect(Collectors.toList()));
            inflowStockQueryParam.setCargoCodeList(feeTotalDetailResultList.stream().map(FeeTotalDetailResult::getCargoCode).distinct().collect(Collectors.toList()));
            inflowStockQueryParam.setImportMonth(feeTotalDetailResultList.get(0).getFeeCycle());
            inflowStockQueryParam.setCurrentPage(page);
            inflowStockQueryParam.setPageSize(pageSize);
            List<InflowStockResult> listInflowStockResult = iInflowStockFacade.pageListInflowStockByInflowStockQueryParam(inflowStockQueryParam).getDataList();
            List<InflowShelfDetailResult> listInflowShelfDetailResult = new ArrayList<>();
            for (FeeTotalDetailResult feeTotalDetailResult : feeTotalDetailResultList) {
                List<InflowShelfDetailResult> inflowShelfDetailResultList = iInflowShelfDetailFacade.SelectMinReceiveDate(feeTotalDetailResult.getCargoCode(),feeTotalDetailResult.getWarehouseCode());
                if (!CollectionUtils.isEmpty(inflowShelfDetailResultList)) {
                    listInflowShelfDetailResult.addAll(inflowShelfDetailResultList);
                }
            }
            List<InflowShelfDetailResult> listInflowShelfDetailNewResult=new ArrayList<>();
            if(listInflowShelfDetailResult!=null && listInflowShelfDetailResult.size()>0) {
                listInflowShelfDetailNewResult=listInflowShelfDetailResult.stream()
                        .sorted(Comparator.comparing(InflowShelfDetailResult::getCreateTime,Comparator.nullsLast(Long::compareTo))).collect(Collectors.toList());
            }
            String dateFirstWarehousing="";
            if(listInflowShelfDetailNewResult!=null && listInflowShelfDetailNewResult.size()>0){
                dateFirstWarehousing=(DateUtil.getDateForLong(listInflowShelfDetailNewResult.get(0).getCreateTime()));
            }
            List<InflowSkuResult> listInflowSkuResult;
            int currentPage=1;
            int size=3000;
            int totalPage;
            InflowSkuQueryParam queryParamSku=new InflowSkuQueryParam();
            queryParamSku.setWarehouseCodeList(feeTotalDetailResultList.stream().map(FeeTotalDetailResult::getWarehouseCode).distinct().collect(Collectors.toList()));
            queryParamSku.setCargoCodeList(feeTotalDetailResultList.stream().map(FeeTotalDetailResult::getCargoCode).distinct().collect(Collectors.toList()));
            queryParamSku.setRealDeleted(true);
            queryParamSku.setCurrentPage(currentPage);
            queryParamSku.setPageSize(size);
            ListVO<InflowSkuResult> listVOInflowSkuResult=iInflowSkuFacade.pageListInflowSkuByInflowSkuQueryParam(queryParamSku);
            listInflowSkuResult=listVOInflowSkuResult.getDataList();
            PageResult pageResult =listVOInflowSkuResult.getPage();
            totalPage=pageResult.getTotalPage();
            for (currentPage=1;currentPage<=totalPage;currentPage++) {
                if(currentPage>1) {
                    queryParamSku.setCurrentPage(currentPage);
                    listVOInflowSkuResult=iInflowSkuFacade.pageListInflowSkuByInflowSkuQueryParam(queryParamSku);
                    List<InflowSkuResult> listNewInflowSkuResult=listVOInflowSkuResult.getDataList();
                    if(listNewInflowSkuResult!=null && listNewInflowSkuResult.size()>0) {
                        listInflowSkuResult.addAll(listNewInflowSkuResult);
                    }
                }
            }
            //仓储费
            //多个计费ID设置一致
            FeeStorageSetQueryParam queryParam = new FeeStorageSetQueryParam();
            queryParam.setContractFeeId(feeTotalDetailVOList.get(0).getContractFeeId());
            queryParam.setFeeClass("仓储费");
            FeeStorageSetResult feeStorageSetResult = feestoragesetFacade.getFeeStorageSetByFeeStorageSetQueryParam(queryParam);
            if (feeStorageSetResult!=null) {
                if(listInflowStockResult!=null && listInflowStockResult.size()>0) {
                    for (InflowStockResult inflowStockResult:listInflowStockResult) {
                        if(inflowStockResult.getPhysicalQty().compareTo(BigDecimal.ZERO) == 0){
                            continue;
                        }
                        FeeStorageExportVO feeCommonExportVO1=new FeeStorageExportVO();
                        feeCommonExportVO1.setFeeDateTime(inflowStockResult.getImportMonthDay());
                        feeCommonExportVO1.setProductCode(inflowStockResult.getSkuCode());
                        feeCommonExportVO1.setSkuQuality(inflowStockResult.getSkuQuality().equals("AVL")?"正品":"次品");
                        feeCommonExportVO1.setVolume(inflowStockResult.getVolume());
                        feeCommonExportVO1.setCargoName(cargoMap.get(inflowStockResult.getCargoCode()));
                        feeCommonExportVO1.setWarehouseName(warehouseMap.get(inflowStockResult.getWarehouseCode()));
                        if(listInflowSkuResult!=null && listInflowSkuResult.size()>0 && inflowStockResult.getSkuCode()!=null) {
                            if(listInflowSkuResult.stream().filter(f->f.getCode().equals(inflowStockResult.getSkuCode())).count()>0) {
                                InflowSkuResult inflowSkuResult=listInflowSkuResult.stream().filter(f->f.getCode().equals(inflowStockResult.getSkuCode())).findFirst().get();
                                if(inflowSkuResult!=null) {
                                    feeCommonExportVO1.setProductName(inflowSkuResult.getName());
                                    feeCommonExportVO1.setLength(inflowStockResult.getLength());
                                    feeCommonExportVO1.setWidth(inflowStockResult.getWidth());
                                    feeCommonExportVO1.setHeight(inflowStockResult.getHeight());
                                }
                            }
                        }
                        if(feeCommonExportVO1.getProductName()==null) {
                            feeCommonExportVO1.setProductName("");
                        }
                        feeCommonExportVO1.setOutStockQty(inflowStockResult.getTempQty());

                        feeCommonExportVO1.setGrossWeight(inflowStockResult.getGrossWeight());
                        listFeeCommonExportVO.add(feeCommonExportVO1);
                    }
                    if(CollectionUtil.isNotEmpty(listFeeCommonExportVO) && page == 1){
                        //C单月份订单总量
                        Integer packageQty = 0;
                        for (FeeTotalDetailResult feeTotalDetailResult : feeTotalDetailResultList) {
                            List<InflowShipmentOrderResult> inflowShipmentOrderResultList = inflowshipmentorderFacade.SelectCommonCountList(feeTotalDetailResult.getWarehouseCode(),feeTotalDetailResult.getCargoCode(),
                                    null,feeTotalDetailResult.getFeeCycle(),null,"B2C");
                            if (!CollectionUtils.isEmpty(inflowShipmentOrderResultList)) {
                                packageQty += inflowShipmentOrderResultList.get(0).getPackageQty();
                            }
                        }
                        listFeeCommonExportVO.get(0).setListPageSize(packageQty);
                        listFeeCommonExportVO.get(0).setDateFirstWarehousing(dateFirstWarehousing);
                    }
                }
            }
        }
        return listFeeCommonExportVO;
    }

    @Override
    public void setSpecialFee(List<FeeStorageExportVO> list, List<FeeTotalDetailVO> feeTotalDetailVOList) {
    }

    @Override
    public WriteSheet buildSheet(FeeTotalDetailVO feeTotalDetailVO, ExportTypeEnum sheetEnum, int size) {
        WriteSheet writeSheet = null;
        //仓储费
        FeeStorageSetQueryParam queryParam = new FeeStorageSetQueryParam();
        queryParam.setContractFeeId(feeTotalDetailVO.getContractFeeId());
        queryParam.setFeeClass("仓储费");
        FeeStorageSetResult feeStorageSetResult = feestoragesetFacade.getFeeStorageSetByFeeStorageSetQueryParam(queryParam);
        if(feeStorageSetResult != null){
            writeSheet = EasyExcel.writerSheet(sheetEnum.getCode(), "恒温仓明细")
                    .head(FeeStorageExportVO.class).registerWriteHandler(new CustomCellWriteHandler()).build();
        }
        return writeSheet;
    }

    @Override
    public ExportTypeEnum getExportTypeEnum() {
        return ExportTypeEnum.FEESTORAGEOTHERTEMP;
    }
}
