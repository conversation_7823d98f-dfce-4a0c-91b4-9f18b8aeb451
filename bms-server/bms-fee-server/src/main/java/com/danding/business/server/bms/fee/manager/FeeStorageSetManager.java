package com.danding.business.server.bms.fee.manager;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.danding.business.client.bms.contract.facade.IContractFacade;
import com.danding.business.client.bms.contract.param.ContractQueryParam;
import com.danding.business.client.bms.contract.result.ContractResult;
import com.danding.business.client.bms.fee.facade.IFeeCalculateConfFacade;
import com.danding.business.client.bms.fee.facade.IFeeCalculateConfLinkFacade;
import com.danding.business.client.bms.fee.param.*;
import com.danding.business.client.bms.fee.result.FeeCalculateConfLinkResult;
import com.danding.business.client.bms.fee.result.FeeCalculateConfResult;
import com.danding.business.client.bms.inflow.facade.IInflowPackageMaterialFacade;
import com.danding.business.client.bms.inflow.facade.IInflowSkuFacade;
import com.danding.business.client.bms.inflow.param.InflowPackageMaterialAddParam;
import com.danding.business.client.bms.inflow.param.InflowPackageMaterialQueryParam;
import com.danding.business.client.bms.inflow.result.InflowPackageMaterialResult;
import com.danding.business.common.bms.emums.FeeTallyReportType;
import com.danding.business.common.bms.emums.PricingType;
import com.danding.business.core.bms.fee.entity.*;
import com.danding.business.core.bms.fee.search.*;
import com.danding.business.core.bms.fee.service.*;
import com.danding.business.server.bms.fee.BO.*;
import com.danding.business.server.bms.fee.manager.helper.FeeStorageSetManagerHelper;
import com.danding.component.boost.annotation.PageSelect;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.soul.client.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 仓储费设置主表(坪效表：dt_fee_storage_effect) 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-18
 */
@Slf4j
@Component
public class FeeStorageSetManager {

    @Autowired
    private IFeeStorageSetService feestoragesetService;

    @Autowired
    private IFeeStorageServiceService feeStorageServiceService;
    @DubboReference
    private IContractFacade contractFacade;
    @Autowired
    private FeeStorageSetManagerHelper feeStorageSetManagerHelper;
    @Autowired
    private IFeeStoragePriceSetService feestoragepricesetService;
    @Autowired
    private IFeeStorageWeightService feestorageweightService;
    @Autowired
    private IFeeStorageEffectService feestorageeffectService;
    @DubboReference
    private IInflowPackageMaterialFacade inflowpackagematerialFacade;
    @Autowired
    private FeeExpressManager feeexpressManager;
    @Autowired
    private FeeLogisticsManager feeLogisticsManager;
    @DubboReference
    private IInflowSkuFacade iInflowSkuFacade;
    @Autowired
    private FeeSimpleConfigManager feeSimpleConfigManager;
    @Autowired
    private IFeeSimpleConfigService feeSimpleConfigService;

    @Autowired
    private FeeTallyReportManager feeTallyReportManager;

    @Autowired
    private FeeSimpleConfigLadderManager feeSimpleConfigLadderManager;

    @DubboReference
    private IFeeCalculateConfFacade feeCalculateConfFacade;

    @DubboReference
    private IFeeCalculateConfLinkFacade feeCalculateConfLinkFacade;

    @Autowired
    private FeeMaterialConfigManager feeMaterialConfigManager;

    /**
     * 列表查询查询
     *
     * @param search
     * @return
     */
    public List<FeeStorageSetBO> listFeeStorageSetByParam(FeeStorageSetSearch search) {
        List<FeeStorageSet> feestorageset = feestoragesetService.selectFeeStorageSetListByParam(search);
        return BeanUtils.copyProperties(feestorageset, FeeStorageSetBO.class);
    }

    /**
     * 分页查询查询
     *
     * @param search
     * @return
     */
    @PageSelect
    public ListVO<FeeStorageSetBO> pageListFeeStorageSetByParam(FeeStorageSetSearch search) {
        List<FeeStorageSet> feestorageset = feestoragesetService.selectFeeStorageSetListByParam(search);
        ListVO<FeeStorageSetBO> listVO = new ListVO<>();
        List<FeeStorageSetBO> bos = BeanUtils.copyProperties(feestorageset, FeeStorageSetBO.class);
        listVO.setDataList(bos);
        return listVO;
    }


    /**
     * id查询单个
     *
     * @param id
     * @return
     */
    public FeeStorageSetBO getFeeStorageSetBOById(Serializable id) {
        return BeanUtils.copyProperties(feestoragesetService.selectById(id), FeeStorageSetBO.class);
    }

    /**
     * 条件查询单个
     *
     * @param feeStorageSetSearch
     * @return
     */
    public FeeStorageSetBO getFeeStorageSetBOByFeeStorageSetSearch(FeeStorageSetSearch feeStorageSetSearch) {
        FeeStorageSet feeStorageSet = feestoragesetService.selectByFeeStorageSetSearch(feeStorageSetSearch);
        return BeanUtils.copyProperties(feeStorageSet, FeeStorageSetBO.class);
    }

    public Map<String, Long> getFkMainId(String oldContractId, String newContractId, String feeClass) {
        Long oldFKMainid = null;
        Long newFKMainid = null;
        Map<String, Long> map = new HashMap<String, Long>();
        FeeStorageSetSearch feeStorageSetSearch = new FeeStorageSetSearch();
        feeStorageSetSearch.setContractFeeId(oldContractId);
        feeStorageSetSearch.setFeeClass(feeClass);
        FeeStorageSet feeStorageSet = feestoragesetService.selectByFeeStorageSetSearch(feeStorageSetSearch);
        if (feeStorageSet != null) {
            oldFKMainid = feeStorageSet.getId();
            feeStorageSet.setId(null);
            feeStorageSet.setContractFeeId(String.valueOf(newContractId));
            this.feestoragesetService.insert(feeStorageSet);
            newFKMainid = feeStorageSet.getId();
            map.put("oldFKMainid", oldFKMainid);
            map.put("newFKMainid", newFKMainid);
            return map;
        } else {
            return null;
        }
    }

    /**
     * 查询有效的仓储费
     * @return
     */
    public List<FeeStorageSetBO> selectFeeStorageSet(){
        List<FeeStorageSet> feeStorageSet = feestoragesetService.selectFeeStorageSet();
        return BeanUtils.copyProperties(feeStorageSet, FeeStorageSetBO.class);
    }

    public void addEffect(Long oldFKMainid, Long newFKMainid) {
        //坪效表/单票入库理货件数与价格表
        FeeStorageEffectSearch effectSearch = new FeeStorageEffectSearch();
        effectSearch.setFkMainId(oldFKMainid);
        List<FeeStorageEffect> feeStorageEffectList = feestorageeffectService.selectListByFeeStorageEffectSearch(effectSearch);
        feeStorageEffectList.forEach(effect -> {
            effect.setId(null);
            effect.setFkMainId(newFKMainid);
        });
        this.feestorageeffectService.insertList(feeStorageEffectList);
    }

    public void addPriceSet(Long oldFKMainid, Long newFKMainid) {
        FeeStoragePriceSetSearch priceSetSearch = new FeeStoragePriceSetSearch();
        priceSetSearch.setFkMainId(oldFKMainid);
        List<FeeStoragePriceSet> feestoragepriceset = feestoragepricesetService.selectFeeStoragePriceSetListByParam(priceSetSearch);
        feestoragepriceset.forEach(price -> {
            price.setId(null);
            price.setFkMainId(newFKMainid);
        });
        this.feestoragepricesetService.insertList(feestoragepriceset);
        //顺丰类操作费
        priceSetSearch.setStatus("SF");
        feestoragepriceset = feestoragepricesetService.selectFeeStoragePriceSetListByParam(priceSetSearch);
        feestoragepriceset.forEach(price -> {
            price.setId(null);
            price.setFkMainId(newFKMainid);
            price.setStatus("SF");
        });
        this.feestoragepricesetService.insertList(feestoragepriceset);
    }


    /**
     * 出入库理货费总额
     *
     * @param feeStorageSetSearch
     * @return
     */
    public TallyFeeSumAmountBO selectTallyCount(FeeStorageSetSearch feeStorageSetSearch) {
        //出入库操作总数
        BigDecimal operationNum = BigDecimal.ZERO;
        //入库操作总数
        BigDecimal inOrderSum = BigDecimal.ZERO;
        //出库操作总数
        BigDecimal outOrderSum = BigDecimal.ZERO;

        BigDecimal countAmount = BigDecimal.ZERO;
        BigDecimal inOrderAmount = BigDecimal.ZERO;
        BigDecimal outOrderAmount = BigDecimal.ZERO;
        //获取到理货费信息
        List<TallyFeeDetailsBO> feeTallyReportBOS = this.selectTallyDetails(feeStorageSetSearch);
        //集合遍历
        for (TallyFeeDetailsBO tallyFeeDetailsBO : feeTallyReportBOS) {
            //操作数总数
            operationNum = operationNum.add(tallyFeeDetailsBO.getOperationNum());
            //理货费总单价
            countAmount = countAmount.add(tallyFeeDetailsBO.getPricing());
            //计算入库理货费操作数，总单价
            if (Objects.equals(tallyFeeDetailsBO.getStatusStr(), FeeTallyReportType.IN_ORDER.name())) {
                //入库理货费总数
                inOrderSum = inOrderSum.add(tallyFeeDetailsBO.getOperationNum());
                //入库理货费总单价
                inOrderAmount = inOrderAmount.add(tallyFeeDetailsBO.getPricing());
            }
            //计算出库理货费操作数，总单价
            if (Objects.equals(tallyFeeDetailsBO.getStatusStr(), FeeTallyReportType.IN_ORDER.name())) {
                //出库理货费总数
                inOrderSum = inOrderSum.add(tallyFeeDetailsBO.getOperationNum());
                //出库理货费总单价
                outOrderAmount = outOrderAmount.add(tallyFeeDetailsBO.getPricing());
            }
        }
        TallyFeeSumAmountBO tallyFeeSumAmountBO = new TallyFeeSumAmountBO();
        tallyFeeSumAmountBO.setSumAmount(countAmount.multiply(operationNum));
        tallyFeeSumAmountBO.setInOrderSumAmount(inOrderAmount.multiply(inOrderSum));
        tallyFeeSumAmountBO.setOutOrderSumAmount(outOrderAmount.multiply(outOrderSum));
        if (Objects.equals(tallyFeeSumAmountBO.getInOrderSumAmount(), BigDecimal.ZERO)) {
            tallyFeeSumAmountBO.setReason("入库单价配置未设置");
        }
        if (Objects.equals(tallyFeeSumAmountBO.getOutOrderSumAmount(), BigDecimal.ZERO)) {
            tallyFeeSumAmountBO.setReason("出库单价配置未设置");
        }
        if (Objects.equals(tallyFeeSumAmountBO.getSumAmount(), BigDecimal.ZERO)) {
            tallyFeeSumAmountBO.setReason("理货费单价配置未设置");
        }
        return tallyFeeSumAmountBO;
    }

    /**
     * 根据货主编码查询理货费详情
     *
     * @param feeStorageSetSearch
     * @return
     */
    @PageSelect
    public List<TallyFeeDetailsBO> selectTallyDetails(FeeStorageSetSearch feeStorageSetSearch) {
        List<TallyFeeDetailsBO> tallyFeeDetailsBOList = new ArrayList<>();
        //把查询条件复制给FeeTallyReportSearch
        FeeTallyReportSearch feeTallyReportSearch = BeanUtils.copyProperties(feeStorageSetSearch, FeeTallyReportSearch.class);
        feeTallyReportSearch.setServiceTimeStart(feeStorageSetSearch.getFeeTallyStaDate());
        feeTallyReportSearch.setServiceTimeEnd(feeStorageSetSearch.getFeeTallyEndDate());
        //获取到理货费信息
        List<FeeTallyReportBO> feeTallyReportBOS = feeTallyReportManager.listBySearch(feeTallyReportSearch);
        if (Objects.isNull(feeTallyReportBOS)) {
            throw new BusinessException("理货费信息未录入" + feeStorageSetSearch.getCargoCode());
        }
        //集合遍历
        for (FeeTallyReportBO feeTallyReportBO : feeTallyReportBOS) {
            TallyFeeDetailsBO tallyFeeDetailsBO = BeanUtils.copyProperties(feeTallyReportBO, TallyFeeDetailsBO.class);
            FeeSimpleConfigSearch feeSimpleConfigSearch = new FeeSimpleConfigSearch();
            feeSimpleConfigSearch.setFeeType("理货费");
            feeSimpleConfigSearch.setFeeClass("理货费");
            feeSimpleConfigSearch.setFeeItem(feeTallyReportBO.getTallyType().getDes());
            feeSimpleConfigSearch.setCategoryId(feeTallyReportBO.getOperationCategoryId());
            feeSimpleConfigSearch.setContractFeeId(feeStorageSetSearch.getContractFeeId());
            //获取配置去单价
            FeeSimpleConfigBO feeSimpleConfigBO = feeSimpleConfigManager.getBySearch(feeSimpleConfigSearch);
            if (Objects.isNull(feeSimpleConfigBO)) {
                throw new BusinessException("理货单未配置");
            }
            Set<Long> idsSet = new HashSet<>();
            idsSet.add(feeSimpleConfigBO.getId());
            //如果是按件数阶梯价就去获取区间价
            if (Objects.equals(feeSimpleConfigBO.getPricingType(), PricingType.LADDER_PRICE)) {
                FeeSimpleConfigLadderSearch feeSimpleConfigLadderSearch = new FeeSimpleConfigLadderSearch();
                feeSimpleConfigLadderSearch.setFeeSimpleConfigIdSet(idsSet);
                List<FeeSimpleConfigLadderBO> ladderBOList = feeSimpleConfigLadderManager.listBySearch(feeSimpleConfigLadderSearch);
                for (FeeSimpleConfigLadderBO feeSimpleConfigLadderBO : ladderBOList) {
                    if (feeTallyReportBO.getOperationNum().compareTo(feeSimpleConfigLadderBO.getSymbolNum()) >= 0) {
                        if (Objects.equals(feeTallyReportBO.getTallyType(), FeeTallyReportType.IN_ORDER)) {
                            tallyFeeDetailsBO.setStatusStr(FeeTallyReportType.IN_ORDER.getDes());
                            tallyFeeDetailsBO.setPricing(feeSimpleConfigLadderBO.getFeePrice());
                        }
                        if (Objects.equals(feeTallyReportBO.getTallyType(), FeeTallyReportType.OUT_ORDER)) {
                            tallyFeeDetailsBO.setStatusStr(FeeTallyReportType.OUT_ORDER.getDes());
                            tallyFeeDetailsBO.setPricing(feeSimpleConfigLadderBO.getFeePrice());
                        }
                    }
                }
            } else {
                if (Objects.equals(feeTallyReportBO.getTallyType(), FeeTallyReportType.IN_ORDER)) {
                    tallyFeeDetailsBO.setStatusStr(FeeTallyReportType.IN_ORDER.getDes());
                    tallyFeeDetailsBO.setPricing(feeSimpleConfigBO.getFeePrice());
                }
                if (Objects.equals(feeTallyReportBO.getTallyType(), FeeTallyReportType.OUT_ORDER)) {
                    tallyFeeDetailsBO.setStatusStr(FeeTallyReportType.OUT_ORDER.getDes());
                    tallyFeeDetailsBO.setPricing(feeSimpleConfigBO.getFeePrice());
                }
            }
            tallyFeeDetailsBOList.add(tallyFeeDetailsBO);
        }
        return tallyFeeDetailsBOList;
    }


    /**
     * 根据新的newContractId生成相关记录
     *
     * @param oldContractId，newContractId,type:1仓储费，2 C单操作费，3理货费，4包材费，5快递费，6物流费,7增值服务费,feeSimpleConfigId
     * @return
     */
    public void addLog(String oldContractId, String newContractId, int type, Long feeSimpleConfigId,FeeExpressAddParam addFeeParam) {
        try {
            if (type != 1) {
                Map<String, Long> map = this.getFkMainId(oldContractId, newContractId, "仓储费");
                if (map != null) {
                    this.addEffect(map.get("oldFKMainid"), map.get("newFKMainid"));//新增FeeStorageEffect
                }
            }
            if (type != 2) {
                Map<String, Long> map = this.getFkMainId(oldContractId, newContractId, "C单操作费");
                if (map != null) {
                    this.addPriceSet(map.get("oldFKMainid"), map.get("newFKMainid"));
                    FeeStorageWeightSearch weightSearch = new FeeStorageWeightSearch();
                    weightSearch.setFkMainId(map.get("oldFKMainid"));
                    List<FeeStorageWeight> feestorageweight = feestorageweightService.selectFeeStorageWeightListByParam(weightSearch);
                    feestorageweight.forEach(weight -> {
                        weight.setId(null);
                        weight.setFkMainId(map.get("newFKMainid"));
                    });
                    this.feestorageweightService.insertList(feestorageweight);

                    FeeStorageServiceSearch serviceSearch = new FeeStorageServiceSearch();
                    serviceSearch.setFkMainId(map.get("oldFKMainid"));
                    List<FeeStorageService> feeStorageServiceList = feeStorageServiceService.selectListBySearch(serviceSearch);
                    feeStorageServiceList.forEach(service -> {
                        service.setId(null);
                        service.setFkMainId(map.get("newFKMainid"));
                    });
                    this.feeStorageServiceService.insertList(feeStorageServiceList);

                    this.addEffect(map.get("oldFKMainid"), map.get("newFKMainid"));//新增FeeStorageEffect
                }
            }
            if (type != 3) {
                Map<String, Long> map = this.getFkMainId(oldContractId, newContractId, "理货费");
                if (map != null) {
                    this.addEffect(map.get("oldFKMainid"), map.get("newFKMainid"));
                    this.addPriceSet(map.get("oldFKMainid"), map.get("newFKMainid"));
                }
            }
            if (type != 4) {
                //包材费
                InflowPackageMaterialQueryParam param = new InflowPackageMaterialQueryParam();
                param.setContractFeeId(oldContractId);
                List<InflowPackageMaterialResult> inflowPackageMaterialResults = inflowpackagematerialFacade.listInflowPackageMaterialByParam(param);
                if (inflowPackageMaterialResults != null) {
                    inflowPackageMaterialResults.forEach(material -> {
                        material.setImportId(null);//包材费里面ImportId才是主键id
                        material.setContractFeeId(String.valueOf(newContractId));
                        material.setUpdateTime(System.currentTimeMillis());
                    });
                    this.inflowpackagematerialFacade.addInflowPackageMaterialList(BeanUtils.copyProperties(inflowPackageMaterialResults, InflowPackageMaterialAddParam.class));
                }
            }
            if (type != 5) {
                //快运费
                FeeExpressQueryParam feeExpressQueryParam = new FeeExpressQueryParam();
                feeExpressQueryParam.setContractFeeId(oldContractId);
                List<FeeExpressBO> feeExpressBOList = this.feeexpressManager.listFeeExpressByParam(BeanUtils.copyProperties(feeExpressQueryParam, FeeExpressSearch.class));
                if (feeExpressBOList != null) {
                    feeExpressBOList.forEach(feeExpressResult -> {
                        Long oldFkMainid = feeExpressResult.getId();
                        feeExpressResult.setId(null);
                        feeExpressResult.setContractFeeId(String.valueOf(newContractId));
                        FeeExpressBO feeExpressBO = this.feeexpressManager.addFeeExpressReturn(BeanUtils.copyProperties(feeExpressResult, FeeExpressBO.class));
                        this.addEffect(oldFkMainid, feeExpressBO.getId());//新增FeeStorageEffect
                    });
                }
            }
            if (type == 5) {
                //复制其它费用类型快递费
                FeeExpressQueryParam feeExpressQueryParam = new FeeExpressQueryParam();
                feeExpressQueryParam.setContractFeeId(oldContractId);
                feeExpressQueryParam.setExpressFeeType(addFeeParam.getExpressFeeType());
                List<FeeExpressBO> feeExpressBOList = this.feeexpressManager.selectFeeExpressList(BeanUtils.copyProperties(feeExpressQueryParam, FeeExpressSearch.class));
                if (feeExpressBOList != null) {
                    feeExpressBOList.forEach(feeExpressResult -> {
                        Long oldFkMainid = feeExpressResult.getId();
                        feeExpressResult.setId(null);
                        feeExpressResult.setContractFeeId(String.valueOf(newContractId));
                        FeeExpressBO feeExpressBO = this.feeexpressManager.addFeeExpressReturn(BeanUtils.copyProperties(feeExpressResult, FeeExpressBO.class));
                        this.addEffect(oldFkMainid, feeExpressBO.getId());//新增FeeStorageEffect
                    });
                }
                //附加费复制基础费用 基础费用复制附加费
                if(addFeeParam.getType().equals("1")){
                    feeExpressQueryParam.setType("2");
                }
                if(addFeeParam.getType().equals("2")){
                    feeExpressQueryParam.setType("1");
                }
                List<FeeExpressBO> expressList = this.feeexpressManager.listFeeExpressByParam(BeanUtils.copyProperties(feeExpressQueryParam, FeeExpressSearch.class));
                if (expressList != null) {
                    expressList.forEach(feeExpressResult -> {
                        Long oldFkMainid = feeExpressResult.getId();
                        feeExpressResult.setId(null);
                        feeExpressResult.setContractFeeId(String.valueOf(newContractId));
                        FeeExpressBO feeExpressBO = this.feeexpressManager.addFeeExpressReturn(BeanUtils.copyProperties(feeExpressResult, FeeExpressBO.class));
                        this.addEffect(oldFkMainid, feeExpressBO.getId());//新增FeeStorageEffect
                    });
                }
            }
            if (type != 6) {
                //物流费
                FeeLogisticsSearch feeLogisticsSearch = new FeeLogisticsSearch();
                feeLogisticsSearch.setContractFeeId(oldContractId);
                List<FeeLogisticsBO> feeLogisticsBOList = this.feeLogisticsManager.listFeeLogisticsByParam(feeLogisticsSearch);
                if (feeLogisticsBOList != null) {
                    feeLogisticsBOList.forEach(feeLogisticsBO -> {
                        feeLogisticsBO.setId(null);
                        feeLogisticsBO.setContractFeeId(newContractId);
                    });
                    this.feeLogisticsManager.addFeeLogisticsList(feeLogisticsBOList);
                }
            }

            FeeSimpleConfigSearch feeSimpleConfigSearch = new FeeSimpleConfigSearch();
            feeSimpleConfigSearch.setContractFeeId(oldContractId);
            feeSimpleConfigSearch.setId(feeSimpleConfigId);
            List<FeeSimpleConfigBO> feeSimpleConfigBOList = this.feeSimpleConfigManager.listBySearch(feeSimpleConfigSearch);
            if (!CollectionUtils.isEmpty(feeSimpleConfigBOList)) {
                for (FeeSimpleConfigBO feeSimpleConfigBO : feeSimpleConfigBOList) {
                    Long oldFeeSimpleConfigId = feeSimpleConfigBO.getId();
                    feeSimpleConfigBO.setId(null);
                    feeSimpleConfigBO.setContractFeeId(newContractId);

                    FeeSimpleConfig feeSimpleConfig = BeanUtils.copyProperties(feeSimpleConfigBO, FeeSimpleConfig.class);
                    feeSimpleConfig.setId(feeSimpleConfigService.addSingle(feeSimpleConfig));

                    //阶梯价insert
                    if (PricingType.LADDER_PRICE.equals(feeSimpleConfigBO.getPricingType())) {
                        FeeSimpleConfigLadderSearch feeSimpleConfigLadderSearch = new FeeSimpleConfigLadderSearch();
                        feeSimpleConfigLadderSearch.setFeeSimpleConfigId(oldFeeSimpleConfigId);
                        List<FeeSimpleConfigLadderBO> ladderBOList = feeSimpleConfigLadderManager.listBySearch(feeSimpleConfigLadderSearch);
                        if (!CollectionUtils.isEmpty(ladderBOList)) {
                            ladderBOList.stream().forEach(ladderBO ->{
                                ladderBO.setId(null);
                                ladderBO.setFeeSimpleConfigId(feeSimpleConfig.getId());
                                ladderBO.setContractFeeId(newContractId);
                            });
                            feeSimpleConfigLadderManager.addList(ladderBOList);
                        }
                    }
                }
            }
            if (type != 7) {
                FeeStorageSetSearch feeStorageSetSearch = new FeeStorageSetSearch();
                feeStorageSetSearch.setFeeClass("增值服务费");
                feeStorageSetSearch.setContractFeeId(oldContractId);
                List<FeeStorageSetBO> feeStorageSetBOS = listFeeStorageSetByParam(feeStorageSetSearch);
                if (CollectionUtil.isNotEmpty(feeStorageSetBOS)) {
                    feeStorageSetBOS.forEach(f -> {
                        Long oldFKMainid = f.getId();
                        f.setId(null);
                        f.setContractFeeId(newContractId);
                        Long newFKMainid = this.addFeeStorageSet(f);
                        FeeStoragePriceSetSearch priceSetSearch = new FeeStoragePriceSetSearch();
                        priceSetSearch.setFkMainId(oldFKMainid);
                        List<FeeStoragePriceSet> feeStoragePriceSets = feestoragepricesetService.selectFeeStoragePriceSetListByParam(priceSetSearch);
                        if (CollectionUtil.isNotEmpty(feeStoragePriceSets)) {
                            this.addPriceSet(oldFKMainid, newFKMainid);
                        }
                    });
                }
            }
            if (type != 8) {
                FeeMaterialConfigSearch feeMaterialConfigSearch = new FeeMaterialConfigSearch();
                feeMaterialConfigSearch.setContractFeeId(oldContractId);
                List<FeeMaterialConfigBO> feeMaterialConfigBOList = feeMaterialConfigManager.listBySearch(feeMaterialConfigSearch);
                if (CollectionUtils.isNotEmpty(feeMaterialConfigBOList)){
                    List<FeeMaterialConfigBO> feeMaterialConfigAddParamList = new ArrayList<>();
                    for (FeeMaterialConfigBO feeMaterialConfigResult : feeMaterialConfigBOList) {
                        feeMaterialConfigResult.setId(null);
                        feeMaterialConfigResult.setContractFeeId(newContractId);
                        feeMaterialConfigAddParamList.add(feeMaterialConfigResult);
                    }
                    feeMaterialConfigManager.addList(feeMaterialConfigAddParamList);
                }
            }
            addNewLog(oldContractId, newContractId);
        } catch (Exception ex) {
            log.error("生成新的费用记录异常 error", ex);
            throw new RuntimeException("生成新的费用记录异常");
        }

    }

    private synchronized void addNewLog(String oldContractId, String newContractId){
        ContractQueryParam contractQueryParam = new ContractQueryParam();
        contractQueryParam.setContractFeeId(oldContractId);
        ContractResult oldContract = contractFacade.getContractByContractQueryParam(contractQueryParam);
        String oldContractVer = oldContract.getContractVer();
        contractQueryParam.setContractFeeId(newContractId);
        ContractResult newContract = contractFacade.getContractByContractQueryParam(contractQueryParam);
        String newContractVer = newContract.getContractVer();

        if (oldContractVer.equals(newContractVer)){
            return;
        }

        FeeCalculateConfQueryParam feeCalculateConfQueryParam = new FeeCalculateConfQueryParam();
        feeCalculateConfQueryParam.setContractVer(oldContractVer);
        List<FeeCalculateConfResult> feeCalculateConfResults = feeCalculateConfFacade.listByQueryParam(feeCalculateConfQueryParam);

        FeeCalculateConfLinkQueryParam feeCalculateConfLinkQueryParam = new FeeCalculateConfLinkQueryParam();
        feeCalculateConfLinkQueryParam.setFkConfIdList(feeCalculateConfResults.stream().map(FeeCalculateConfResult::getId).collect(Collectors.toList()));
        List<FeeCalculateConfLinkResult> feeCalculateConfLinkResults = feeCalculateConfLinkFacade.listByQueryParam(feeCalculateConfLinkQueryParam);
        Map<Long, List<FeeCalculateConfLinkResult>> confLinkMap = feeCalculateConfLinkResults.stream().collect(Collectors.groupingBy(FeeCalculateConfLinkResult::getFkConfId));

        if (CollectionUtils.isNotEmpty(feeCalculateConfResults)){

            feeCalculateConfQueryParam.setContractVer(newContractVer);
            if (CollectionUtil.isNotEmpty(feeCalculateConfFacade.listByQueryParam(feeCalculateConfQueryParam))){
                feeCalculateConfFacade.removeByQueryParam(feeCalculateConfQueryParam);
            }

            for (FeeCalculateConfResult feeCalculateConfResult : feeCalculateConfResults) {
                FeeCalculateConfAddParam addParam = BeanUtils.copyProperties(feeCalculateConfResult, FeeCalculateConfAddParam.class);
                addParam.setId(null);
                addParam.setContractVer(newContractVer);
                addParam.setContractFeeId(newContractId);
                Long insertId = feeCalculateConfFacade.insert(addParam);

                List<FeeCalculateConfLinkResult> linkResults = confLinkMap.get(feeCalculateConfResult.getId());
                if (CollectionUtils.isNotEmpty(linkResults) && insertId != null){
                    for (FeeCalculateConfLinkResult feeCalculateConfLinkResult : linkResults) {
                        FeeCalculateConfLinkAddParam linkAddParam = BeanUtils.copyProperties(feeCalculateConfLinkResult, FeeCalculateConfLinkAddParam.class);
                        linkAddParam.setId(null);
                        linkAddParam.setFkConfId(insertId);
                        feeCalculateConfLinkFacade.add(linkAddParam);
                    }
                }
            }
        }
    }

    /**
     * 列表查询
     *
     * @param feeStorageSetSearch
     * @return
     */
    public List<FeeStorageSetBO> listFeeStorageSetBOByFeeStorageSetSearch(FeeStorageSetSearch feeStorageSetSearch) {
        List<FeeStorageSet> feeStorageSetList = feestoragesetService.selectListByFeeStorageSetSearch(feeStorageSetSearch);
        return BeanUtils.copyProperties(feeStorageSetList, FeeStorageSetBO.class);
    }

    /**
     * 分页查询
     *
     * @param feeStorageSetSearch
     * @return
     */
    @PageSelect
    public ListVO<FeeStorageSetBO> pageListFeeStorageSetBOByFeeStorageSetSearch(FeeStorageSetSearch feeStorageSetSearch) {
        ListVO<FeeStorageSetBO> feeStorageSetBOListVO = new ListVO<>();
        List<FeeStorageSet> feeStorageSetList = feestoragesetService.selectListByFeeStorageSetSearch(feeStorageSetSearch);
        return ListVO.build(feeStorageSetBOListVO.getPage(), BeanUtils.copyProperties(feeStorageSetList, FeeStorageSetBO.class));
    }


    /**
     * 功能描述:  插入
     */
    public Long addFeeStorage(FeeStorageSetBO feeStorageSetBO) {
        FeeStorageSet feeStorageSet = BeanUtils.copyProperties(feeStorageSetBO, FeeStorageSet.class);
        boolean insert = feestoragesetService.insert(feeStorageSet);
        if (insert) {
            return feeStorageSet.getId();
        }
        return null;
    }


    /**
     * 功能描述:  插入
     */
    public Long addFeeStorageSet(FeeStorageSetBO feeStorageSetBO) {
        FeeStorageSet feeStorageSet = BeanUtils.copyProperties(feeStorageSetBO, FeeStorageSet.class);
        boolean insert = feestoragesetService.insert(feeStorageSet);
        if (insert) {
            return feeStorageSet.getId();
        }
        return null;
    }

    /**
     * 功能描述:  批量插入
     */
    public boolean addFeeStorageSetList(List<FeeStorageSetBO> feeStorageSetBOList) {
        return feestoragesetService.insertList(BeanUtils.copyProperties(feeStorageSetBOList, FeeStorageSet.class));
    }

    /**
     * 功能描述:  根据主键id修改
     */
    public boolean updateFeeStorageSetById(FeeStorageSetBO feeStorageSetBO) {
        return feestoragesetService.updateById(BeanUtils.copyProperties(feeStorageSetBO, FeeStorageSet.class));
    }

    /**
     * 功能描述:  根据主键id批量修改
     */
    public boolean updateFeeStorageSetListById(List<FeeStorageSetBO> feeStorageSetBOList) {
        return feestoragesetService.updateListById(BeanUtils.copyProperties(feeStorageSetBOList, FeeStorageSet.class));
    }

    /**
     * 功能描述:  根据条件修改
     */
    public boolean updateFeeStorageSetListByFeeStorageSetSearch(FeeStorageSetSearch feeStorageSetSearch, FeeStorageSetBO feeStorageSetBO) {
        return feestoragesetService.updateListByFeeStorageSetSearch(feeStorageSetSearch, BeanUtils.copyProperties(feeStorageSetBO, FeeStorageSet.class));
    }

    /**
     * 功能描述:  根据主键id删除
     */
    public boolean removeFeeStorageSetByIds(Serializable id) {
        return feestoragesetService.deleteById(id);
    }

    /**
     * 功能描述:  根据主键id批量删除
     */
    public boolean removeFeeStorageSetByIds(List<Long> idList) {
        return feestoragesetService.deleteByIds(idList);
    }

    /**
     * 功能描述:  根据条件删除
     */
    public boolean removeFeeStorageSetByFeeStorageSetSearch(FeeStorageSetSearch feeStorageSetSearch) {
        return feestoragesetService.deleteByFeeStorageSetSearch(feeStorageSetSearch);
    }

}
