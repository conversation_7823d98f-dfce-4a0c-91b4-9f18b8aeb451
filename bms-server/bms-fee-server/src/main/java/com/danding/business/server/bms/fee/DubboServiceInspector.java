package com.danding.business.server.bms.fee;

import com.alibaba.com.caucho.hessian.io.*;
import com.alibaba.com.caucho.hessian.io.java8.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.ClassUtils;
import org.apache.dubbo.config.ServiceConfig;
import org.apache.dubbo.config.spring.ReferenceBean;
import org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.beans.factory.annotation.InjectionMetadata;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.beans.factory.support.AbstractBeanFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import javax.management.ObjectName;
import java.io.File;
import java.io.Serializable;
import java.lang.reflect.*;
import java.math.BigDecimal;
import java.util.*;

import static com.alibaba.com.caucho.hessian.io.java8.Java8TimeSerializer.create;


@Slf4j
@Component
@Profile({"local", "dev", "DEV", "integration", "test", "pre"})
public class DubboServiceInspector implements SmartInitializingSingleton {

    private final ApplicationContext applicationContext;

    private static HashMap _staticSerializerMap = new HashMap();

    private static List<String> packagePrefixList = new ArrayList<>();

    // 要忽略的字段类型
    private static final List<String> filterClassList = new ArrayList<>();
    // 要忽略的RPC接口类型
    private static final List<String> filterLocalProviderList = new ArrayList<>();

    static {
        packagePrefixList.add("com.danding");
        packagePrefixList.add("com.dt");

        filterClassList.add("org.slf4j.Logger");
        filterClassList.add("com.baomidou.mybatisplus.core.mapper.BaseMapper");
        filterClassList.add("java.util.function.Function");

        filterLocalProviderList.add("com.danding.component.common.rpc.common.service.IBoostDubboService");


        addBasic(void.class, "void", BasicSerializer.NULL);

        addBasic(Boolean.class, "boolean", BasicSerializer.BOOLEAN);
        addBasic(Byte.class, "byte", BasicSerializer.BYTE);
        addBasic(Short.class, "short", BasicSerializer.SHORT);
        addBasic(Integer.class, "int", BasicSerializer.INTEGER);
        addBasic(Long.class, "long", BasicSerializer.LONG);
        addBasic(Float.class, "float", BasicSerializer.FLOAT);
        addBasic(Double.class, "double", BasicSerializer.DOUBLE);
        addBasic(Character.class, "char", BasicSerializer.CHARACTER_OBJECT);
        addBasic(String.class, "string", BasicSerializer.STRING);
        addBasic(Object.class, "object", BasicSerializer.OBJECT);
        addBasic(Date.class, "date", BasicSerializer.DATE);

        addBasic(boolean.class, "boolean", BasicSerializer.BOOLEAN);
        addBasic(byte.class, "byte", BasicSerializer.BYTE);
        addBasic(short.class, "short", BasicSerializer.SHORT);
        addBasic(int.class, "int", BasicSerializer.INTEGER);
        addBasic(long.class, "long", BasicSerializer.LONG);
        addBasic(float.class, "float", BasicSerializer.FLOAT);
        addBasic(double.class, "double", BasicSerializer.DOUBLE);
        addBasic(char.class, "char", BasicSerializer.CHARACTER);

        addBasic(boolean[].class, "[boolean", BasicSerializer.BOOLEAN_ARRAY);
        addBasic(byte[].class, "[byte", BasicSerializer.BYTE_ARRAY);
        addBasic(short[].class, "[short", BasicSerializer.SHORT_ARRAY);
        addBasic(int[].class, "[int", BasicSerializer.INTEGER_ARRAY);
        addBasic(long[].class, "[long", BasicSerializer.LONG_ARRAY);
        addBasic(float[].class, "[float", BasicSerializer.FLOAT_ARRAY);
        addBasic(double[].class, "[double", BasicSerializer.DOUBLE_ARRAY);
        addBasic(char[].class, "[char", BasicSerializer.CHARACTER_ARRAY);
        addBasic(String[].class, "[string", BasicSerializer.STRING_ARRAY);
        addBasic(Object[].class, "[object", BasicSerializer.OBJECT_ARRAY);

        _staticSerializerMap.put(Class.class, new ClassSerializer());
        _staticSerializerMap.put(BigDecimal.class, new StringValueSerializer());
        _staticSerializerMap.put(UUID.class, new StringValueSerializer());
        _staticSerializerMap.put(File.class, new StringValueSerializer());
        _staticSerializerMap.put(ObjectName.class, new StringValueSerializer());
        _staticSerializerMap.put(java.sql.Date.class, new SqlDateSerializer());
        _staticSerializerMap.put(java.sql.Time.class, new SqlDateSerializer());
        _staticSerializerMap.put(java.sql.Timestamp.class, new SqlDateSerializer());
        _staticSerializerMap.put(java.io.InputStream.class,
                new InputStreamSerializer());

        try {
            if (isJava8()) {
                _staticSerializerMap.put(Class.forName("java.time.LocalTime"), create(LocalTimeHandle.class));
                _staticSerializerMap.put(Class.forName("java.time.LocalDate"), create(LocalDateHandle.class));
                _staticSerializerMap.put(Class.forName("java.time.LocalDateTime"), create(LocalDateTimeHandle.class));

                _staticSerializerMap.put(Class.forName("java.time.Instant"), create(InstantHandle.class));
                _staticSerializerMap.put(Class.forName("java.time.Duration"), create(DurationHandle.class));
                _staticSerializerMap.put(Class.forName("java.time.Period"), create(PeriodHandle.class));

                _staticSerializerMap.put(Class.forName("java.time.Year"), create(YearHandle.class));
                _staticSerializerMap.put(Class.forName("java.time.YearMonth"), create(YearMonthHandle.class));
                _staticSerializerMap.put(Class.forName("java.time.MonthDay"), create(MonthDayHandle.class));

                _staticSerializerMap.put(Class.forName("java.time.OffsetDateTime"), create(OffsetDateTimeHandle.class));
                _staticSerializerMap.put(Class.forName("java.time.ZoneOffset"), create(ZoneOffsetHandle.class));
                _staticSerializerMap.put(Class.forName("java.time.OffsetTime"), create(OffsetTimeHandle.class));
                _staticSerializerMap.put(Class.forName("java.time.ZonedDateTime"), create(ZonedDateTimeHandle.class));
            }
        } catch (Throwable t) {
            log.warn(String.valueOf(t.getCause()));
        }
    }

    private static void addBasic(Class cl, String typeName, int type) {
        _staticSerializerMap.put(cl, new BasicSerializer(type));
    }

    public DubboServiceInspector(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    @Override
    public void afterSingletonsInstantiated() {
        Map<Class<?>, Object> localProviderInterfaceMap = new HashMap<>();
        Map<Type, Method> localProviderMap = inspectLocalProvider(localProviderInterfaceMap);

        Map<Type, Method> remoteProviderMap =  inspectRemoteProvider();

        System.out.println("**********************************************************************");

        for (Map.Entry<Class<?>, Object> entry : localProviderInterfaceMap.entrySet()) {
            Class<?> type = entry.getKey();
            Object implClass = entry.getValue();
            System.out.println(String.format("error implements: %s in %s", type.getTypeName(), implClass.getClass().getName()));
        }

        System.out.println("**********************************************************************");
        for (Map.Entry<Type, Method> entry : localProviderMap.entrySet()) {
            Type type = entry.getKey();
            Method method = entry.getValue();
            String classMethodName = method.getDeclaringClass().getName() + "#" + method.getName();
            System.out.println(String.format("Local Provider: %s in %s", type.getTypeName(), classMethodName));
        }

        for (Map.Entry<Type, Method> entry : remoteProviderMap.entrySet()) {
            Type type = entry.getKey();
            Method method = entry.getValue();
            String classMethodName = method.getDeclaringClass().getName() + "#" + method.getName();
            System.out.println(String.format("Remote Provider: %s in %s", type.getTypeName(), classMethodName));
        }
        System.out.println("**********************************************************************");

        if(localProviderMap.size() > 0 || remoteProviderMap.size() > 0){
            throw new RuntimeException("存在未实现Serializable的RPC传输对象");
        }
        if(localProviderInterfaceMap.size() > 0){
            throw new RuntimeException("存在非法实现接口的Dubbo服务提供者");
        }
    }


    private Map<Type, Method> inspectLocalProvider(Map<Class<?>, Object> localProviderInterfaceMap) {
        Set<Type> serializableSet = new HashSet<>();
        Map<Type, Method> nonSerializableSet = new HashMap<>();

        Map<String, ServiceConfig> serviceConfigMap = applicationContext.getBeansOfType(ServiceConfig.class);
        for (Map.Entry<String, ServiceConfig> entry : serviceConfigMap.entrySet()) {
            ServiceConfig serviceConfig = entry.getValue();
            Class<?> ultimateTargetClass = serviceConfig.getInterfaceClass(); // 获取实际的业务类
            if(filterLocalProviderList.contains(ultimateTargetClass.getName())){
                continue;
            }

            // 检测dubbo service实现的接口是否合法
            if (packagePrefixList.stream().noneMatch(ultimateTargetClass.getName()::startsWith)) {
                localProviderInterfaceMap.put(ultimateTargetClass, serviceConfig.getRef());
            }
            registerInterface(serializableSet, nonSerializableSet, ultimateTargetClass.getMethods(), ultimateTargetClass);
        }

        return nonSerializableSet;
    }

    public Map<Type, Method> inspectRemoteProvider() {
        Set<Type> serializableSet = new HashSet<>();
        Map<Type, Method> nonSerializableSet = new HashMap<>();

        Map<InjectionMetadata.InjectedElement, ReferenceBean<?>> referenceBeans = getReferenceAnnotationBeanPostProcessor(applicationContext).getInjectedFieldReferenceBeanMap();
        for (Map.Entry<InjectionMetadata.InjectedElement, ReferenceBean<?>> entry : referenceBeans.entrySet()) {
            ReferenceBean<?> referenceBean = entry.getValue();
            Class<?> objectType = referenceBean.getObjectType();

            Method[] methodsToExport = objectType.getMethods();
            registerInterface(serializableSet, nonSerializableSet, methodsToExport, objectType);
        }

        return nonSerializableSet;
    }

    public static synchronized void registerInterface(Set<Type> serializableSet, Map<Type, Method> nonSerializableSet, Method[] methodsToExport, Class<?> objectType) {
        for (Method method : methodsToExport) {
            // 获取方法的所有参数的类型（非泛型类型）。
            Class<?>[] parameterTypes = method.getParameterTypes();
            for (Class<?> parameterType : parameterTypes) {
                checkClass(serializableSet, nonSerializableSet, parameterType, method, objectType);
            }

            // 获取方法的所有参数的泛型类型（如 List<String> 这种类型）。
            Type[] genericParameterTypes = method.getGenericParameterTypes();
            for (Type genericParameterType : genericParameterTypes) {
                checkType(serializableSet, nonSerializableSet, genericParameterType, method, objectType);
            }

            // 获取方法的返回类型（非泛型类型）。
            Class<?> returnType = method.getReturnType();
            checkClass(serializableSet, nonSerializableSet, returnType, method, objectType);

            // 获取方法的泛型返回类型。
            Type genericReturnType = method.getGenericReturnType();
            checkType(serializableSet, nonSerializableSet, genericReturnType, method, objectType);

        }
    }

    private static void checkType(Set<Type> markedClass, Map<Type, Method> nonSerializableSet, Type type, Method method, Class<?> objectType) {
        if (type == null) {
            return;
        }
        if (type instanceof Class) {
            checkClass(markedClass, nonSerializableSet, (Class<?>) type, method, objectType);
            return;
        }

        if (!markedClass.add(type)) {
            return;
        }

        if (type instanceof ParameterizedType) {
            ParameterizedType parameterizedType = (ParameterizedType) type;
            checkClass(markedClass, nonSerializableSet, (Class<?>) parameterizedType.getRawType(), method, objectType);
            for (Type actualTypeArgument : parameterizedType.getActualTypeArguments()) {
                checkType(markedClass, nonSerializableSet, actualTypeArgument, method, objectType);
            }
        } else if (type instanceof GenericArrayType) {
            GenericArrayType genericArrayType = (GenericArrayType) type;
            checkType(markedClass, nonSerializableSet, genericArrayType.getGenericComponentType(), method, objectType);
        } else if (type instanceof TypeVariable) {
            TypeVariable typeVariable = (TypeVariable) type;
            for (Type bound : typeVariable.getBounds()) {
                checkType(markedClass, nonSerializableSet, bound, method, objectType);
            }
        } else if (type instanceof WildcardType) {
            WildcardType wildcardType = (WildcardType) type;
            for (Type bound : wildcardType.getUpperBounds()) {
                checkType(markedClass, nonSerializableSet, bound, method, objectType);
            }
            for (Type bound : wildcardType.getLowerBounds()) {
                checkType(markedClass, nonSerializableSet, bound, method, objectType);
            }
        }
    }

    private static void checkClass(Set<Type> markedClass, Map<Type, Method> nonSerializableSet, Class<?> clazz, Method method, Class<?> objectType) {
        if (clazz == null) {
            return;
        }
//        if("com.baomidou.mybatisplus.core.mapper.BaseMapper".equals(clazz.getName())){
//            // debug
//            System.out.println("1");
//        }

        if (!markedClass.add(clazz)) {
            return;
        }

        if (ClassUtils.isSimpleType(clazz) || clazz.isPrimitive() || clazz.isArray()) {
            return;
        }
        String className = clazz.getName();
        if (
//                className.startsWith("java.")
//                || className.startsWith("javax.")
//                || className.startsWith("com.sun.")
//                || className.startsWith("sun.")
//                || className.startsWith("jdk.")
                _staticSerializerMap.containsKey(clazz)
                // @Slf4j编译后添加的字段 可忽略
                || className.equals("org.slf4j.Logger")
                || className.equals("com.baomidou.mybatisplus.core.mapper.BaseMapper")
                || className.equals("java.util.function.Function")) {
            return;
        }

        // 校验入参类型
        validateSerializerImplementation(clazz, method, nonSerializableSet);

        // 取所有字段集合
        Field[] fields = getAllFields(clazz);
        for (Field field : fields) {
            if (Modifier.isTransient(field.getModifiers())) {
                continue;
            }

            Class<?> fieldClass = field.getType();
            checkClass(markedClass, nonSerializableSet, fieldClass, method, objectType);
            checkType(markedClass, nonSerializableSet, field.getGenericType(), method, objectType);
        }
        // a 继承 b  同时实现Serializable  b未实现Serializable  这种情况Hessian2不会校验b 他也是取class的所有字段再进行校验
//        // TODO 使用较为严格的检查思路
//        Field[] fields = clazz.getDeclaredFields();
//
//        for (Field field : fields) {
//            if (Modifier.isTransient(field.getModifiers())) {
//                continue;
//            }
//
//            Class<?> fieldClass = field.getType();
//            checkClass(markedClass, nonSerializableSet, fieldClass, method, objectType);
//            checkType(markedClass, nonSerializableSet, field.getGenericType(), method, objectType);
//        }
//        Class<?> superclass = clazz.getSuperclass();
//        if (superclass != null) {
//            checkClass(markedClass, nonSerializableSet, superclass, method, objectType);
//        }
//
//        Type genericSuperclass = clazz.getGenericSuperclass();
//        if (genericSuperclass != null) {
//            checkType(markedClass, nonSerializableSet, genericSuperclass, method, objectType);
//        }

    }

    public static Field[] getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();
        while (clazz != null) {
            Field[] declaredFields = clazz.getDeclaredFields();
            for (Field field : declaredFields) {
                fields.add(field);
            }
            clazz = clazz.getSuperclass();
        }
        return fields.toArray(new Field[0]);
    }

    private static void validateSerializerImplementation(Class<?> clazz, Method method, Map<Type, Method> nonSerializableSet) {
        if (clazz.isPrimitive() ||
                clazz == String.class ||
                clazz == Object.class ||
                Number.class.isAssignableFrom(clazz) ||
                    clazz == Boolean.class ||
                clazz == Character.class ||
                clazz == Date.class ||
                Collection.class.isAssignableFrom(clazz) ||
                Map.class.isAssignableFrom(clazz)) {
            return;
        }

        if (Serializable.class.isAssignableFrom(clazz)) {
            return;
        }
        nonSerializableSet.put(clazz, method);
//        throw new RuntimeException("Class: " + clazz.getName() + " does not implement Serializable.");
    }

    static ReferenceAnnotationBeanPostProcessor getReferenceAnnotationBeanPostProcessor(
            AbstractBeanFactory beanFactory) {
        for (BeanPostProcessor beanPostProcessor : beanFactory.getBeanPostProcessors()) {
            if (beanPostProcessor instanceof ReferenceAnnotationBeanPostProcessor) {
                return (ReferenceAnnotationBeanPostProcessor) beanPostProcessor;
            }
        }
        return null;
    }

    static ReferenceAnnotationBeanPostProcessor getReferenceAnnotationBeanPostProcessor(
            ApplicationContext applicationContext) {
        return getReferenceAnnotationBeanPostProcessor(
                (AbstractBeanFactory) applicationContext.getAutowireCapableBeanFactory());
    }

    /**
     * check if the environment is java 8 or beyond
     *
     * @return if on java 8
     */
    private static boolean isJava8() {
        String javaVersion = System.getProperty("java.specification.version");
        return Double.valueOf(javaVersion) >= 1.8;
    }
}
