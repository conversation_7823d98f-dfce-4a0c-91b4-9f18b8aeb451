package com.danding.business.server.bms.fee.facade;

import com.danding.business.client.bms.dict.result.DictResult;
import com.danding.business.client.bms.fee.facade.IFeeItemsFacade;
import com.danding.business.client.bms.fee.facade.IFeeOtherFacade;
import com.danding.business.client.bms.fee.param.FeeItemsQueryParam;
import com.danding.business.client.bms.fee.result.FeeItemsResult;
import com.danding.business.client.bms.fee.result.FeeOtherListResult;
import com.danding.business.core.bms.fee.search.FeeOtherListSearch;
import com.danding.business.server.bms.fee.manager.FeeOtherManager;
import com.danding.business.client.bms.fee.result.FeeOtherResult;
import com.danding.business.client.bms.fee.param.FeeOtherQueryParam;
import com.danding.business.client.bms.fee.param.FeeOtherAddParam;
import com.danding.business.core.bms.fee.search.FeeOtherSearch;
import com.danding.business.server.bms.fee.BO.FeeOtherBO;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.DubboService;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import org.springframework.data.redis.core.RedisTemplate;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.io.Serializable;
import java.util.Map;

/**
 * <p>
 * 其他费用表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-05
 */
@DubboService
public class FeeOtherFacadeImpl implements IFeeOtherFacade {

    @Autowired
    private FeeOtherManager feeotherManager;

    @DubboReference
    private IFeeItemsFacade feeitemsFacade;

    @Resource(name = "redisNewTemplate")
    private RedisTemplate redisTemplate;

    /**
     * 其他费用帅选查询列表
     *
     * @param queryParam
     * @return
     */
    @Override
    public ListVO<FeeOtherListResult> SelectOtherFeeList(FeeOtherQueryParam queryParam){

        FeeOtherSearch search = BeanUtils.copyProperties(queryParam, FeeOtherSearch.class);
        FeeItemsQueryParam feeItemsQueryParam=new FeeItemsQueryParam();
        feeItemsQueryParam.setFeeCode(queryParam.getFeeCode());
        feeItemsQueryParam.setId(queryParam.getFkFeeId());
        List<FeeItemsResult> feeItemsResult =new ArrayList<>();
        if(queryParam.getFkFeeId()!=null && queryParam.getFkFeeId()>0)
        {
            feeItemsResult =feeitemsFacade.listFeeItemsByFeeItemsQueryParam(feeItemsQueryParam);
        }
        if(feeItemsResult!=null && feeItemsResult.size()>0)
        {
            search.setFkFeeId(feeItemsResult.get(0).getId());
        }

        List<FeeOtherListSearch> list= feeotherManager.SelectOtherFeeList(search);
        ListVO<FeeOtherListResult> resultListVO = new ListVO<>();

        Map<String, DictResult> redisDataMap =redisTemplate.opsForHash().entries("allDict:JF001");
        Map<String, DictResult> redisDataMap2 =  redisTemplate.opsForHash().entries("allDict:DW001");
        Map<String, DictResult> redisDataMap3 =  redisTemplate.opsForHash().entries("allDict:SS001");
        Map<String, FeeItemsResult> redisDeeItemDataMap = redisTemplate.opsForHash().entries("allDict:allFeeItemDict");
        List<FeeOtherListResult> results =new ArrayList<>();
        for (FeeOtherListSearch bo:list) {
            FeeOtherListResult feeOther = BeanUtils.copyProperties(bo, FeeOtherListResult.class);
            if(redisDataMap!=null && redisDataMap.containsKey(feeOther.getStatus()))
            {
                ObjectMapper mapper = new ObjectMapper();
                DictResult dictResult = mapper.convertValue(redisDataMap.get(feeOther.getStatus()),DictResult.class);
                if(dictResult!=null)
                {
                    //状态名称
                    bo.setStatusName(dictResult.getDictValue());
                }
            }

            if(redisDataMap2!=null && redisDataMap2.containsKey(feeOther.getUnit().toString()))
            {
                ObjectMapper mapper = new ObjectMapper();
                DictResult dictResult = mapper.convertValue(redisDataMap2.get(feeOther.getUnit()),DictResult.class);
                if(dictResult!=null)
                {
                    //单位名称
                    bo.setUnitName(dictResult.getDictValue());
                }
            }

            if(redisDataMap3!=null && redisDataMap3.containsKey(feeOther.getServiceStatus().toString()))
            {
                ObjectMapper mapper = new ObjectMapper();
                DictResult dictResult = mapper.convertValue(redisDataMap3.get(feeOther.getServiceStatus()),DictResult.class);
                if(dictResult!=null)
                {
                    //服务状态名称
                    bo.setServiceStatusName(dictResult.getDictValue());
                }
            }

            if(redisDeeItemDataMap!=null && redisDeeItemDataMap.containsKey(feeOther.getFkFeeId().toString()))
            {
                ObjectMapper mapper = new ObjectMapper();
                FeeItemsResult dictResult = mapper.convertValue(redisDeeItemDataMap.get(feeOther.getFkFeeId()),FeeItemsResult.class);
                if(dictResult!=null)
                {
                    //服务状态名称
                    bo.setFeeName(dictResult.getFeeName());
                }
            }
            results.add(feeOther);
        }
        //List<FeeOtherListResult> results = BeanUtils.copyProperties(list, FeeOtherListResult.class);
        resultListVO.setDataList(results);
        resultListVO.setPage(resultListVO.getPage());

        return resultListVO;
    }


    @Override
    public List<FeeOtherResult> listFeeJobOtherByParam(FeeOtherQueryParam queryParam) {
        FeeOtherSearch search = BeanUtils.copyProperties(queryParam, FeeOtherSearch.class);
        List<FeeOtherBO> bos = feeotherManager.listFeeJobOtherByParam(search);
        if (CollectionUtils.isNotEmpty(bos)) {
            return BeanUtils.copyProperties(bos, FeeOtherResult.class);
        }
        return null;
    }

    @Override
    public List<FeeOtherResult> listFeeOtherByParam(FeeOtherQueryParam queryParam) {
        FeeOtherSearch search = BeanUtils.copyProperties(queryParam, FeeOtherSearch.class);
        List<FeeOtherBO> bos = feeotherManager.listFeeOtherByParam(search);
        if (CollectionUtils.isNotEmpty(bos)) {
        return BeanUtils.copyProperties(bos, FeeOtherResult.class);
        }
        return null;
    }


    @Override
    public ListVO<FeeOtherResult> pageListFeeOtherByParam(FeeOtherQueryParam queryParam) {
        FeeOtherSearch search = BeanUtils.copyProperties(queryParam, FeeOtherSearch.class);
        ListVO<FeeOtherBO> listVO = feeotherManager.pageListFeeOtherByParam(search);
        ListVO<FeeOtherResult> resultListVO = new ListVO<>();

        List<FeeOtherResult> results = BeanUtils.copyProperties(listVO.getDataList(), FeeOtherResult.class);
        resultListVO.setDataList(results);
        resultListVO.setPage(listVO.getPage());
        return resultListVO;
    }



    /**
     * 主键查询
     *
     * @param id
     * @return
     */
    @Override
    public FeeOtherResult getFeeOtherById(Serializable id) {
        FeeOtherBO feeOtherBO = feeotherManager.getFeeOtherBOById(id);
        return BeanUtils.copyProperties(feeOtherBO, FeeOtherResult.class);
    }

    /**
     * 条件查询单个
     *
     * @param feeOtherQueryParam
     * @return
     */
    @Override
    public FeeOtherResult getFeeOtherByFeeOtherQueryParam(FeeOtherQueryParam feeOtherQueryParam) {
        FeeOtherBO feeOtherBO = feeotherManager.getFeeOtherBOByFeeOtherSearch(BeanUtils.copyProperties(feeOtherQueryParam, FeeOtherSearch.class));
        return BeanUtils.copyProperties(feeOtherBO, FeeOtherResult.class);
    }

    /**
     * 条件查询list
     *
     * @param feeOtherQueryParam
     * @return
     */
    @Override
    public List<FeeOtherResult> listFeeOtherByFeeOtherQueryParam(FeeOtherQueryParam feeOtherQueryParam) {
        List<FeeOtherBO> feeOtherBOList = feeotherManager.listFeeOtherBOByFeeOtherSearch(BeanUtils.copyProperties(feeOtherQueryParam, FeeOtherSearch.class));
        return BeanUtils.copyProperties(feeOtherBOList, FeeOtherResult.class);
    }

    /**
     * 条件分页查询
     *
     * @param feeOtherQueryParam
     * @return
     */
    @Override
    public ListVO<FeeOtherResult> pageListFeeOtherByFeeOtherQueryParam(FeeOtherQueryParam feeOtherQueryParam) {
        ListVO<FeeOtherBO> feeOtherBOListVO = feeotherManager.pageListFeeOtherBOByFeeOtherSearch(BeanUtils.copyProperties(feeOtherQueryParam, FeeOtherSearch.class));
        return ListVO.build(feeOtherBOListVO.getPage(), BeanUtils.copyProperties(feeOtherBOListVO.getDataList(), FeeOtherResult.class));
    }

    /**
     * 插入
     *
     * @param feeOtherAddParam
     * @return
     */
    @Override
    public boolean addFeeOther(FeeOtherAddParam feeOtherAddParam) {
        return feeotherManager.addFeeOther(BeanUtils.copyProperties(feeOtherAddParam, FeeOtherBO.class));
    }

    /**
     * 批量插入
     *
     * @param feeOtherAddParamList
     * @return
     */
    @Override
    public boolean addFeeOtherList(List<FeeOtherAddParam> feeOtherAddParamList) {
        List<FeeOtherBO> listFeeOtherBO=BeanUtils.copyProperties(feeOtherAddParamList, FeeOtherBO.class);
        Boolean isSuccess=feeotherManager.addFeeOtherList(listFeeOtherBO);
        feeOtherAddParamList=BeanUtils.copyProperties(listFeeOtherBO, FeeOtherAddParam.class);
        return isSuccess;
    }

    /**
     * 根据主键id修改
     *
     * @param feeOtherAddParam
     * @return
     */
    @Override
    public boolean updateFeeOtherById(FeeOtherAddParam feeOtherAddParam) {
        return feeotherManager.updateFeeOtherById(BeanUtils.copyProperties(feeOtherAddParam, FeeOtherBO.class));
    }

    /**
     * 根据主键id批量修改
     *
     * @param feeOtherAddParamList
     * @return
     */
    @Override
    public boolean updateFeeOtherListById(List<FeeOtherAddParam> feeOtherAddParamList) {
        return feeotherManager.updateFeeOtherListById(BeanUtils.copyProperties(feeOtherAddParamList, FeeOtherBO.class));
    }

    /**
     * 根据条件修改
     *
     * @param feeOtherQueryParam
     * @param feeOtherAddParam
     * @return
     */
    @Override
    public boolean updateFeeOtherListByFeeOtherQueryParam(FeeOtherQueryParam feeOtherQueryParam, FeeOtherAddParam feeOtherAddParam) {
        return feeotherManager.updateFeeOtherListByFeeOtherSearch(BeanUtils.copyProperties(feeOtherQueryParam, FeeOtherSearch.class), BeanUtils.copyProperties(feeOtherAddParam, FeeOtherBO.class));
    }

    /**
     * 根据主键id删除
     *
     * @param id
     * @return
     */
    @Override
    public boolean removeFeeOtherById(Serializable id) {
        return feeotherManager.removeFeeOtherByIds(id);
    }

    /**
     * 根据主键id批量删除
     *
     * @param idList
     * @return
     */
    @Override
    public boolean removeFeeOtherByIds(List<Long> idList) {
        return feeotherManager.removeFeeOtherByIds(idList);
    }

    /**
     * 根据条件删除
     *
     * @param feeOtherQueryParam
     * @return
     */
    @Override
    public boolean removeFeeOtherByFeeOtherQueryParam(FeeOtherQueryParam feeOtherQueryParam) {
        return feeotherManager.removeFeeOtherByFeeOtherSearch(BeanUtils.copyProperties(feeOtherQueryParam, FeeOtherSearch.class));
    }

}
