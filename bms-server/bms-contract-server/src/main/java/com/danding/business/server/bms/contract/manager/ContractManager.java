package com.danding.business.server.bms.contract.manager;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.danding.business.core.bms.contract.entity.Contract;
import com.danding.business.core.bms.contract.search.ContractSearch;
import com.danding.business.core.bms.contract.service.IContractService;
import com.danding.business.server.bms.contract.BO.ContractBO;
import com.danding.business.server.bms.contract.manager.helper.ContractManagerHelper;
import com.danding.component.boost.annotation.PageSelect;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.api.common.response.PageResult;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 合同表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-05
 */
@Component
public class ContractManager {

    @Autowired
    private IContractService contractService;

    @Autowired
    private ContractManagerHelper contractManagerHelper;
    @Resource(name = "redisNewTemplate")
    private RedisTemplate redisTemplate;
    private final static String KEY = "BMS:NOTSTANDARDUSER";

    public Boolean isStorageFeeChange(String contractCode,String month){
        return contractService.isStorageFeeChange(contractCode,month);
    }

    @PageSelect
    public ListVO<ContractBO> SelectPageSearchByParam(ContractSearch contractSearch) {
        List<Contract> contractList = contractService.SelectPageSearchByParam(contractSearch);
        ListVO<ContractBO> listVO = new ListVO<>();
        List<ContractBO> bos = BeanUtils.copyProperties(contractList, ContractBO.class);
        listVO.setDataList(bos);
        return listVO;
    }




    /**
     * 列表查询查询
     *
     * @param search
     * @return
     */
    public List<ContractBO> listCheckContractByParam(ContractSearch search) {
        List<Contract> contract = contractService.selectCheckContractListByParam(search);
        if (CollectionUtils.isNotEmpty(contract)) {
            return BeanUtils.copyProperties(contract, ContractBO.class);
        }
        return null;
    }


    /**
     * 列表查询查询
     *
     * @param search
     * @return
     */
    public List<ContractBO> listContractByParam(ContractSearch search) {
        List<Contract> contract = contractService.selectContractListByParam(search);
        return BeanUtils.copyProperties(contract, ContractBO.class);
    }

    public List<ContractBO> listNewContractByParam(ContractSearch search) {
        List<Contract> contract = contractService.selectNewContractListByParam(search);
        return BeanUtils.copyProperties(contract, ContractBO.class);
    }

    public List<ContractBO> searchHavingInvoicing() {
        List<Contract> contract = contractService.searchHavingInvoicing();
        return BeanUtils.copyProperties(contract, ContractBO.class);
    }

    /**
     * 货主编码+仓编码唯一性校验
     * @param contract
     * @return
     */
    public List<Contract>checkContract(Contract contract){
        return this.contractService.checkContract(contract);
    }


    public ContractBO selectLastContractByParam(ContractSearch search) {
        Contract contract = contractService.selectLastContractByParam(search);
        return BeanUtils.copyProperties(contract, ContractBO.class);
    }


    public List<ContractBO> listJobContractByParam(ContractSearch search) {
        List<Contract> contract = contractService.selectJobContractListByParam(search);
        return BeanUtils.copyProperties(contract, ContractBO.class);
    }


    public List<ContractBO> selectJobRetryContractListByParam(ContractSearch search) {
        List<Contract> contract = contractService.selectJobRetryContractListByParam(search);
        if (CollectionUtils.isNotEmpty(contract)) {
            return BeanUtils.copyProperties(contract, ContractBO.class);
        }
        return null;
    }

    @PageSelect
    public ListVO<ContractBO> listCargoCodeContractByParam(ContractSearch search) {
        List<Contract> contract = contractService.listCargoCodeContractByParam(search);
        ListVO<ContractBO> listVO = new ListVO<>();
        if (CollectionUtils.isNotEmpty(contract)) {
            List<ContractBO> bos = BeanUtils.copyProperties(contract, ContractBO.class);
            listVO.setDataList(bos);

            return listVO;
        }
        return null;
    }


    /**
     * 分页查询查询
     *
     * @param search
     * @return
     */
    @PageSelect
    public ListVO<ContractBO> pageListContractByParam(ContractSearch search) {
        List<Contract> contract = contractService.pageContractListByParam(search);
        ListVO<ContractBO> listVO = new ListVO<>();
        List<ContractBO> bos = BeanUtils.copyProperties(contract, ContractBO.class);
        listVO.setDataList(bos);
        return listVO;
    }



    /**
     * id查询单个
     *
     * @param id
     * @return
     */
    public ContractBO getContractBOById(Serializable id) {
        return BeanUtils.copyProperties(contractService.selectById(id), ContractBO.class);
    }

    /**
     * 条件查询单个
     *
     * @param contractSearch
     * @return
     */
    public ContractBO getContractBOByContractSearch(ContractSearch contractSearch) {
        Contract contract = contractService.selectByContractSearch(contractSearch);
        return BeanUtils.copyProperties(contract, ContractBO.class);
    }

    /**
     * 查询临时版本记录
     * @param search
     * @return
     */
    public ContractBO selectByContractOne(ContractSearch search){
        Contract contract =this.contractService.selectByContractOne(search);
       return BeanUtils.copyProperties(contract, ContractBO.class);
    }


    /**
     * workBench
     * @param
     * @return
     */
    public List<ContractBO> workBench(ContractSearch contractSearch){
        List<Contract> contractList = contractService.workBench(contractSearch.getWarehouseCodeList());
        return BeanUtils.copyProperties(contractList, ContractBO.class);
    }

    /**
     * 列表查询
     *
     * @param contractSearch
     * @return
     */
    public List<ContractBO> listContractBOByContractSearch(ContractSearch contractSearch) {
        List<Contract> contractList = contractService.selectListByContractSearch(contractSearch);
        return BeanUtils.copyProperties(contractList, ContractBO.class);
    }

    /**
     * 列表查询
     *
     * @param contractSearch
     * @return
     */
    public List<ContractBO> ListContractSearch(ContractSearch contractSearch) {
        List<Contract> contractList = contractService.ListContractSearch(contractSearch);
        return BeanUtils.copyProperties(contractList, ContractBO.class);
    }

    /**
     * 分页查询
     *
     * @param contractSearch
     * @return
     */
    @PageSelect
    public ListVO<ContractBO> pageListContractBOByContractSearch(ContractSearch contractSearch) {
        ListVO<ContractBO> contractBOListVO = new ListVO<>();
        List<Contract> list = new ArrayList<>();
        List<Contract> contractList = contractService.selectListByContractSearch(contractSearch);
        List<Contract> alllist = this.contractService.selectBatchContract(contractList);
        for (Contract contract : contractList) {
            Contract cont = new Contract();
            Contract cont1 = new Contract();
            for (Contract ct : alllist) {
                if (contract.getCargoCode().equals(ct.getCargoCode()) && contract.getWarehouseCode().equals(ct.getWarehouseCode())) {
                    if (ct.getStatus().equals("0")) {
                        cont = ct;
                    } else {
                        cont1 = ct;
                    }
                }
            }
            if (cont.getId() > 0) {
                list.add(cont);
            } else {
                list.add(cont1);
            }
        }
        return ListVO.build(contractBOListVO.getPage(), BeanUtils.copyProperties(list, ContractBO.class));
    }

    /**
     * 分页查询
     */
    public ListVO<ContractBO> selectContractLogPage(ContractSearch search) {
        List<Contract> contractList = contractService.searchLogVer(search);
        List<Contract> contractBOS = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(contractList)){
            for(Contract contract : contractList){
                ContractSearch contractSearch = new ContractSearch();
                contractSearch.setContractVer(contract.getContractVer());
                List<Contract> contracts = contractService.selectContractListByParam(contractSearch);
                if(CollectionUtil.isNotEmpty(contracts)){
                    contractBOS.add(contracts.get(0));
                }
            }
        }
        List<Contract> subList = contractBOS.stream().skip((search.getCurrentPage()-1)*search.getPageSize()).limit(search.getPageSize()).
                collect(Collectors.toList());
        PageResult pageResult = new PageResult();
        double size = contractBOS.size();
        double page = search.getPageSize();
        double totalPage = Math.ceil(size/page);
        pageResult.setTotalPage((int) totalPage);
        pageResult.setPageSize(search.getPageSize());
        pageResult.setTotalCount(contractBOS.size());
        pageResult.setCurrentPage(search.getCurrentPage());
        return ListVO.build(pageResult, BeanUtils.copyProperties(subList, ContractBO.class));
    }

    public List<ContractBO> selectContractLog(ContractSearch contractSearch) {
        List<Contract> contractList = contractService.selectContractLogPage(contractSearch);
        return BeanUtils.copyProperties(contractList, ContractBO.class);
    }

    /**
     * 功能描述:  插入
     */
    public boolean addContract(ContractBO contractBO) {
        Contract contract = BeanUtils.copyProperties(contractBO, Contract.class);
        Boolean ret = contractService.insert(contract);
        contractBO.setId(contract.getId());
        return ret;
    }

    /**
     * 功能描述:  插入
     */
    public Long intertContract(ContractBO contractBO) {
        Contract contract = BeanUtils.copyProperties(contractBO, Contract.class);
        if (Objects.isNull(contract.getId())) {
            contract.setCreateTime(null);
            contract.setUpdateTime(null);
        }
        contractService.insert(contract);
        return contract.getId();
    }

    /**
     * 功能描述:  批量插入
     */
    public boolean addContractList(List<ContractBO> contractBOList) {
        return contractService.insertList(BeanUtils.copyProperties(contractBOList, Contract.class));
    }

    /**
     * 功能描述:  根据主键id修改
     */
    public boolean updateContractById(ContractBO contractBO) {
        return contractService.updateById(BeanUtils.copyProperties(contractBO, Contract.class));
    }

    /**
     * 功能描述:  根据主键id批量修改
     */
    public boolean updateContractListById(List<ContractBO> contractBOList) {
        return contractService.updateListById(BeanUtils.copyProperties(contractBOList, Contract.class));
    }

    /**
     * 功能描述:  根据条件修改
     */
    public boolean updateContractListByContractSearch(ContractSearch contractSearch, ContractBO contractBO) {
        return contractService.updateListByContractSearch(contractSearch, BeanUtils.copyProperties(contractBO, Contract.class));
    }

    /**
     * 功能描述:  根据主键id删除
     */
    public boolean removeContractByIds(Serializable id) {
        return contractService.deleteById(id);
    }

    /**
     * 功能描述:  根据主键id批量删除
     */
    public boolean removeContractByIds(List<Long> idList) {
        return contractService.deleteByIds(idList);
    }

    /**
     * 功能描述:  根据条件删除
     */
    public boolean removeContractByContractSearch(ContractSearch contractSearch) {
        return contractService.deleteByContractSearch(contractSearch);
    }

    /**
     * 搜索
     * @param search
     * @return
     */
    public ListVO<ContractBO> pagSearchByParam(ContractSearch search) {
        if(search.getIfStandard() != null){
            Object value = redisTemplate.opsForValue().get(KEY);
            if(value != null){
                search.setCargoCodeList(Arrays.asList(value.toString().split(",")));
            }
        }
        List<Contract> groupContractList = contractService.pagSearchByParam(search);
        if(CollectionUtil.isNotEmpty(groupContractList)){
            List<Contract> stopList = groupContractList.stream().filter(c -> c.getStatus().equals("7")).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(stopList)){
                groupContractList.removeAll(stopList);
                groupContractList.addAll(stopList);
            }
        }
        List<Contract> subList = groupContractList.stream().skip((search.getCurrentPage()-1)*search.getPageSize()).limit(search.getPageSize()).
                collect(Collectors.toList());
        PageResult pageResult = new PageResult();
        double size = groupContractList.size();
        double page = search.getPageSize();
        double totalPage = Math.ceil(size/page);
        pageResult.setTotalPage((int) totalPage);
        pageResult.setPageSize(search.getPageSize());
        pageResult.setTotalCount(groupContractList.size());
        pageResult.setCurrentPage(search.getCurrentPage());
        return ListVO.build(pageResult, BeanUtils.copyProperties(subList, ContractBO.class));
    }


    public List<ContractBO> searchNewByParam(ContractSearch search) {
        List<Contract> groupContractList = contractService.pagSearchByParam(search);
        if(CollectionUtil.isNotEmpty(groupContractList)){
            List<Contract> stopList = groupContractList.stream().filter(c -> c.getStatus().equals("7")).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(stopList)){
                groupContractList.removeAll(stopList);
                groupContractList.addAll(stopList);
            }
        }
        return BeanUtils.copyProperties(groupContractList, ContractBO.class);
    }

    public List<ContractBO> searchNewContract(ContractSearch search) {
        return BeanUtils.copyProperties(contractService.searchMax(search), ContractBO.class);
    }

    public Long updateContract(ContractSearch search){
        return this.contractService.updateContract(BeanUtils.copyProperties(search, Contract.class));
    }

    public List<Contract>queryContractByTime(){
        return this.contractService.queryContractByTime();
    }

    public int stopContract(Contract contract){
        return this.contractService.stopContract(contract);
    }

    public List<ContractBO> warehouseCostSelect(ContractSearch contractSearch) {
        return BeanUtils.copyProperties(contractService.searchByQueryForCost(contractSearch), ContractBO.class);
    }

    public ListVO<ContractBO> pageWarehouseCost(ContractSearch contractSearch) {
        ListVO<ContractBO> contractBOListVO = new ListVO<>();
        return ListVO.build(contractBOListVO.getPage(), BeanUtils.copyProperties(contractService.searchByQueryForCost(contractSearch), ContractBO.class));
    }

    public ContractBO warehouseCostGetOne(ContractSearch contractSearch) {
        return BeanUtils.copyProperties(contractService.getByQueryForCost(contractSearch), ContractBO.class);
    }

    public ListVO<ContractBO> pageWarehouseCostSelect(ContractSearch contractSearch) {
        ListVO<ContractBO> contractBOListVO = new ListVO<>();
        return ListVO.build(contractBOListVO.getPage(), BeanUtils.copyProperties(contractService.pageByQueryForCost(contractSearch), ContractBO.class));
    }

    public Boolean updateContractByCost(Contract contract, ContractSearch contractSearch) {
        return contractService.updateContractByCost(contract, contractSearch);
    }
}
