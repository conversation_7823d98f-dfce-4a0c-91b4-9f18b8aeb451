package com.danding.business.client.bms.imports.result;


import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import com.danding.component.common.base.DO.BaseEntity;

/**
 * <p>
 * 三级库存快照返回结果
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-31
 */

@Data
@ApiModel(value="StockLocationSnapshot对象", description="三级库存快照返回结果")
public class StockLocationSnapshotResult  implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;
    /**
     * 是否删除(1:未删除  2:删除)
     */
    @TableLogic
    private Integer deleted;


   /**
    * 快照時間
    */
    @ApiModelProperty(value = "快照時間")
    private Long snapshotTime;

   /**
    * 原表主键id
    */
    @ApiModelProperty(value = "原表主键id")
    private Long hid;

   /**
    * 仓库编码
    */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

   /**
    * 货主编码
    */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

   /**
    * 商品编码
    */
    @ApiModelProperty(value = "商品编码")
    private String skuCode;

   /**
    * 库区编码
    */
    @ApiModelProperty(value = "库区编码")
    private String zoneCode;

   /**
    * 库区类型
    */
    @ApiModelProperty(value = "库区类型")
    private String zoneType;

   /**
    * 库位编码
    */
    @ApiModelProperty(value = "库位编码")
    private String locationCode;

   /**
    * 库位类型
    */
    @ApiModelProperty(value = "库位类型")
    private String locationType;

   /**
    * 库位用途
    */
    @ApiModelProperty(value = "库位用途")
    private String locationUseMode;

   /**
    * 批次ID
    */
    @ApiModelProperty(value = "批次ID")
    private String skuLotNo;

   /**
    * 实物数量
    */
    @ApiModelProperty(value = "实物数量")
    private BigDecimal physicalQty;

   /**
    * 冻结数量
    */
    @ApiModelProperty(value = "冻结数量")
    private BigDecimal frozenQty;

   /**
    * 占用数量
    */
    @ApiModelProperty(value = "占用数量")
    private BigDecimal occupyQty;

   /**
    * 可用数量 实物库存-冻结-占用=可用数
    */
    @ApiModelProperty(value = "可用数量 实物库存-冻结-占用=可用数")
    private BigDecimal availableQty;

   /**
    * 状态码
    */
    @ApiModelProperty(value = "状态码")
    private Integer status;

   /**
    * 创建人
    */
    @ApiModelProperty(value = "创建人")
    private String createdBy;

   /**
    * 创建时间 (时间戳)
    */
    @ApiModelProperty(value = "创建时间 (时间戳)")
    private Long createdTime;

   /**
    * 更新人
    */
    @ApiModelProperty(value = "更新人")
    private String updatedBy;

   /**
    * 更新时间 (时间戳)
    */
    @ApiModelProperty(value = "更新时间 (时间戳)")
    private Long updatedTime;

   /**
    * 待上架数量
    */
    @ApiModelProperty(value = "待上架数量")
    private BigDecimal waitShelfQty;

   /**
    * 商品属性
    */
    @ApiModelProperty(value = "商品属性")
    private String skuQuality;

    /**
     * 库位计费规则
     */
    @ApiModelProperty(value = "库位计费规则")
    private BigDecimal chargingModel;

    /**
     * 是否恒温 Y N
     */
    private String thermostatic;

    /**
     * 入库日期
     */
    private Long receiveDate;

    /**
     * 仓储日期
     */
    private String storageDate;
}
