package com.danding.business.client.bms.imports.facade;


import com.danding.business.client.bms.imports.result.SkuResult;
import com.danding.business.client.bms.imports.result.StockResult;
import com.danding.business.client.bms.imports.param.StockQueryParam;
import com.danding.business.client.bms.imports.param.StockAddParam;

import com.danding.component.common.api.common.response.ListVO;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 一级库存 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-25
 */
public interface IStockFacade {

    ListVO<StockResult> SelectCommonList(StockQueryParam queryParam);

    List<StockResult> listStockByParam(StockQueryParam queryParam);

    ListVO<StockResult> pageListStockByParam(StockQueryParam queryParam);



    /**
     * 主键查询
     *
     * @param id
     * @return
     */
    StockResult getStockById(Serializable id) ;

    /**
     * 条件查询单个
     *
     * @param stockQueryParam
     * @return
     */
    StockResult getStockByStockQueryParam(StockQueryParam stockQueryParam) ;

    /**
     * 条件查询list
     *
     * @param stockQueryParam
     * @return
     */
    List<StockResult> listStockByStockQueryParam(StockQueryParam stockQueryParam) ;

    /**
     * 条件分页查询
     *
     * @param stockQueryParam
     * @return
     */
    ListVO<StockResult> pageListStockByStockQueryParam(StockQueryParam stockQueryParam) ;

    /**
     * 插入
     *
     * @param stockAddParam
     * @return
     */
    boolean addStock(StockAddParam stockAddParam) ;

    /**
     * 批量插入
     *
     * @param stockAddParamList
     * @return
     */
    boolean addStockList(List<StockAddParam> stockAddParamList) ;

    /**
     * 根据主键id修改
     *
     * @param stockAddParam
     * @return
     */
    boolean updateStockById(StockAddParam stockAddParam) ;

    /**
     * 根据主键id批量修改
     *
     * @param stockAddParamList
     * @return
     */
    boolean updateStockListById(List<StockAddParam> stockAddParamList) ;

    /**
     * 根据条件修改
     *
     * @param stockQueryParam
     * @param stockAddParam
     * @return
     */
    boolean updateStockListByStockQueryParam(StockQueryParam stockQueryParam, StockAddParam stockAddParam) ;

    /**
     * 根据主键id删除
     *
     * @param id
     * @return
     */
    boolean removeStockById(Serializable id) ;

    /**
     * 根据主键id批量删除
     *
     * @param idList
     * @return
     */
    boolean removeStockByIds(List<Long> idList) ;

    /**
     * 根据条件删除
     *
     * @param stockQueryParam
     * @return
     */
    boolean removeStockByStockQueryParam(StockQueryParam stockQueryParam) ;

}
