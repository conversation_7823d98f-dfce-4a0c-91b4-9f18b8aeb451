package com.danding.business.client.bms.data.param;

import com.danding.component.common.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 查询
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-30
 */

@Data
@ApiModel(value="DataOwner对象", description="查询")
public class DataOwnerQueryParam extends Page {


    private static final long serialVersionUID = 1L;

    /**
    * id
    */
    private Long id;


    /**
     * 实体仓编码
     */
    @ApiModelProperty(value = "实体仓编码")
    private String warehouseCode;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String code;

    /**
     * 货主名称
     */
    @ApiModelProperty(value = "货主名称")
    private String name;

    /**
     * 来源
     */
    @ApiModelProperty(value = "来源")
    private String source;

    /**
     * 同步时间
     */
    @ApiModelProperty(value = "同步时间")
    private Long sycTime;


}
