package com.danding.business.client.bms.inflow.param;

import com.danding.component.common.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 清关单查询
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-08
 */

@Data
@ApiModel(value="InventoryOrderInfo对象", description="清关单查询")
public class InflowCustomsClearanceOrderSearchParam extends Page {

    private static final long serialVersionUID = 1L;

    /**
     * 完成时间
     */
    private Date finishTimeStart;
    /**
     * 完成时间
     */
    private Date finishTimeEnd;

}
