package com.danding.business.client.bms.inflow.param;

import com.danding.component.common.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 出库单查询
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-12
 */

@Data
@ApiModel(value="InflowShipmentOrderSnapshot对象", description="出库单查询")
public class InflowShipmentOrderSnapshotQueryParam extends Page {


    private static final long serialVersionUID = 1L;
    private Long id;
    /**
    * 创建时间
    */
    private Long createTime;
    /**
    * 更新时间
    */
    private Long updateTime;
    /**
    * 创建人
    */
    private Long createBy;
    /**
    * 更新人
    */
    private Long updateBy;

    /**
     * 导入主键ID.不允许重复（仓库+id）
     */
    @ApiModelProperty(value = "导入主键ID.不允许重复（仓库+id）")
    private String importId;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    /**
     * 出库单号
     */
    @ApiModelProperty(value = "出库单号")
    private String shipmentOrderCode;

    /**
     * 客户单号
     */
    @ApiModelProperty(value = "客户单号")
    private String poNo;

    /**
     * 上游单号
     */
    @ApiModelProperty(value = "上游单号")
    private String soNo;

    /**
     * 交易单号
     */
    @ApiModelProperty(value = "交易单号")
    private String tradeNo;

    /**
     * 单据状态
     */
    @ApiModelProperty(value = "单据状态")
    private String status;

    /**
     * 预处理状态
     */
    @ApiModelProperty(value = "预处理状态")
    private String pretreatmentStatus;

    /**
     * 单据类型
     */
    @ApiModelProperty(value = "单据类型")
    private String orderType;

    /**
     * 保税类型
     */
    @ApiModelProperty(value = "保税类型")
    private String taxType;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String businessType;

    /**
     * 销售平台
     */
    @ApiModelProperty(value = "销售平台")
    private String salePlatform;

    /**
     * 销售店铺ID
     */
    @ApiModelProperty(value = "销售店铺ID")
    private String saleShopId;

    /**
     * 销售店铺
     */
    @ApiModelProperty(value = "销售店铺")
    private String saleShop;

    /**
     * 快递公司编码
     */
    @ApiModelProperty(value = "快递公司编码")
    private String carrierCode;

    /**
     * 快递公司名称
     */
    @ApiModelProperty(value = "快递公司名称")
    private String carrierName;

    /**
     * 快递单号
     */
    @ApiModelProperty(value = "快递单号")
    private String expressNo;

    /**
     * 包裹类型
     */
    @ApiModelProperty(value = "包裹类型")
    private String packageStruct;

    /**
     * 汇单时间
     */
    @ApiModelProperty(value = "汇单时间")
    private Long collectTime;

    /**
     * 预计出库时间
     */
    @ApiModelProperty(value = "预计出库时间")
    private Long expOutStockDate;

    /**
     * 交易下单时间
     */
    @ApiModelProperty(value = "交易下单时间")
    private Long placeTradeOrderDate;

    /**
     * 付款时间
     */
    @ApiModelProperty(value = "付款时间")
    private Long payDate;

    /**
     * 拣货开始时间
     */
    @ApiModelProperty(value = "拣货开始时间")
    private Long pickSkuDate;

    /**
     * 拣货完成时间
     */
    @ApiModelProperty(value = "拣货完成时间")
    private Long pickCompleteSkuDate;

    /**
     * 复核开始时间
     */
    @ApiModelProperty(value = "复核开始时间")
    private Long checkStartDate;

    /**
     * 复核完成时间
     */
    @ApiModelProperty(value = "复核完成时间")
    private Long checkCompleteDate;

    /**
     * 首包裹出库时间
     */
    @ApiModelProperty(value = "首包裹出库时间")
    private Long firstPackOutStockDate;

    /**
     * 出库时间
     */
    @ApiModelProperty(value = "出库时间")
    private Long outStockDate;

    /**
     * 拦截取消时间
     */
    @ApiModelProperty(value = "拦截取消时间")
    private Long interceptCancelDate;

    /**
     * 商品属性
     */
    @ApiModelProperty(value = "商品属性")
    private String skuQuality;

    /**
     * 商品品种数
     */
    @ApiModelProperty(value = "商品品种数")
    private Integer skuTypeQty;

    /**
     * 订单商品数量
     */
    @ApiModelProperty(value = "订单商品数量")
    private BigDecimal skuQty;

    /**
     * 出库商品数量
     */
    @ApiModelProperty(value = "出库商品数量")
    private BigDecimal outSkuQty;

    /**
     * 包裹数量
     */
    @ApiModelProperty(value = "包裹数量")
    private Integer packageQty;

    /**
     * 出库包裹数
     */
    @ApiModelProperty(value = "出库包裹数")
    private Integer outPackageQty;

    /**
     * 回传通知状态
     */
    @ApiModelProperty(value = "回传通知状态")
    private Integer notifyStatus;

    @ApiModelProperty(value = "")
    private Integer notifyCount;

    /**
     * 回传通知时间
     */
    @ApiModelProperty(value = "回传通知时间")
    private Long notifyTime;

    /**
     * 收货人姓名
     */
    @ApiModelProperty(value = "收货人姓名")
    private String receiverMan;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String receiverTel;

    /**
     * 收货国家
     */
    @ApiModelProperty(value = "收货国家")
    private String receiverCountry;

    /**
     * 收货省份
     */
    @ApiModelProperty(value = "收货省份")
    private String receiverProv;

    /**
     * 收货人市
     */
    @ApiModelProperty(value = "收货人市")
    private String receiverCity;

    /**
     * 收货人区
     */
    @ApiModelProperty(value = "收货人区")
    private String receiverArea;

    /**
     * 收货省份名称
     */
    @ApiModelProperty(value = "收货省份名称")
    private String receiverProvName;

    /**
     * 收货人市名称
     */
    @ApiModelProperty(value = "收货人市名称")
    private String receiverCityName;

    /**
     * 收货人区名称
     */
    @ApiModelProperty(value = "收货人区名称")
    private String receiverAreaName;

    /**
     * 收货人邮编
     */
    @ApiModelProperty(value = "收货人邮编")
    private String receiverZipcode;

    /**
     * 收货人详细地址
     */
    @ApiModelProperty(value = "收货人详细地址")
    private String receiverAddress;

    /**
     * 发货人
     */
    @ApiModelProperty(value = "发货人")
    private String senderMan;

    /**
     * 发货人电话
     */
    @ApiModelProperty(value = "发货人电话")
    private String senderTel;

    /**
     * 发货人国家
     */
    @ApiModelProperty(value = "发货人国家")
    private String senderCountry;

    /**
     * 发货人省
     */
    @ApiModelProperty(value = "发货人省")
    private String senderProv;

    /**
     * 发货人市
     */
    @ApiModelProperty(value = "发货人市")
    private String senderCity;

    /**
     * 发货人区
     */
    @ApiModelProperty(value = "发货人区")
    private String senderArea;

    @ApiModelProperty(value = "")
    private String senderProvName;

    @ApiModelProperty(value = "")
    private String senderCityName;

    @ApiModelProperty(value = "")
    private String senderAreaName;

    @ApiModelProperty(value = "")
    private String senderZipcode;

    /**
     * 发货人地址
     */
    @ApiModelProperty(value = "发货人地址")
    private String senderAddress;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * (真实同步数据删除状态)逻辑删除 -1 删除 1 
     */
    @ApiModelProperty(value = "(真实同步数据删除状态)逻辑删除 -1 删除 1 ")
    private Boolean realDeleted;

    /**
     * 导入月份
     */
    @ApiModelProperty(value = "导入月份")
    private String importMonth;

    /**
     * 导入月份日期
     */
    @ApiModelProperty(value = "导入月份日期")
    private String importMonthDay;

    /**
     * 导入修改时间
     */
    @ApiModelProperty(value = "导入修改时间")
    private Long updatedTime;

    /**
     * 导入创建时间
     */
    @ApiModelProperty(value = "导入创建时间")
    private Long createdTime;

    /**
     * 原销售店铺名
     */
    @ApiModelProperty(value = "原销售店铺名")
    private String internalShopName;

    /**
     * 原销售平台名
     */
    @ApiModelProperty(value = "原销售平台名")
    private String originalPlatform;

    /**
     * 拓传字段json
     */
    @ApiModelProperty(value = "拓传字段json")
    private String extraJson;

    /**
     * 计费版本
     */
    @ApiModelProperty(value = "计费版本")
    private Long feeVer;

    @ApiModelProperty(value = "开始")
    private Long strAcceptDateStart;

    @ApiModelProperty(value = "结束")
    private Long strAcceptDateEnd;

    private List<Long> feeVerList;

    private List<String> cargoCodeList;

    private List<String> warehouseCodes;

    private Long exDate;
}
