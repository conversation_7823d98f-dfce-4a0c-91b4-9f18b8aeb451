package com.danding.business.client.bms.fee.facade;


import com.danding.business.client.bms.fee.param.FeeValueAddedServiceNewAddParam;
import com.danding.business.client.bms.fee.param.FeeValueAddedServiceNewQueryParam;
import com.danding.business.client.bms.fee.result.FeeValueAddedServiceNewResult;
import com.danding.component.common.api.common.response.ListVO;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 增值服务费-新表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
public interface IFeeValueAddedServiceNewFacade {

    int selectCountBySearch(FeeValueAddedServiceNewQueryParam feeValueAddedServiceQueryParam);

    boolean submitByIds(List<Long> idList, Long userId);

    boolean cancelByIds(List<Long> idList);
    /**
     * 主键查询
     *
     * @param id
     * @return
     */
    FeeValueAddedServiceNewResult getById(Serializable id) ;

    /**
     * 条件查询单个
     *
     * @param feeValueAddedServiceNewQueryParam
     * @return
     */
    FeeValueAddedServiceNewResult getByQueryParam(FeeValueAddedServiceNewQueryParam feeValueAddedServiceNewQueryParam) ;

    /**
     * 条件查询list
     *
     * @param feeValueAddedServiceNewQueryParam
     * @return
     */
    List<FeeValueAddedServiceNewResult> listByQueryParam(FeeValueAddedServiceNewQueryParam feeValueAddedServiceNewQueryParam) ;

    /**
     * 条件分页查询
     *
     * @param feeValueAddedServiceNewQueryParam
     * @return
     */
    ListVO<FeeValueAddedServiceNewResult> pageListByQueryParam(FeeValueAddedServiceNewQueryParam feeValueAddedServiceNewQueryParam) ;

    /**
     * 插入
     *
     * @param feeValueAddedServiceNewAddParam
     * @return
     */
    boolean add(FeeValueAddedServiceNewAddParam feeValueAddedServiceNewAddParam) ;

    /**
     * 批量插入
     *
     * @param feeValueAddedServiceNewAddParamList
     * @return
     */
    boolean addList(List<FeeValueAddedServiceNewAddParam> feeValueAddedServiceNewAddParamList) ;

    /**
     * 根据主键id修改
     *
     * @param feeValueAddedServiceNewAddParam
     * @return
     */
    boolean updateById(FeeValueAddedServiceNewAddParam feeValueAddedServiceNewAddParam) ;

    /**
     * 根据主键id批量修改
     *
     * @param feeValueAddedServiceNewAddParamList
     * @return
     */
    boolean updateListById(List<FeeValueAddedServiceNewAddParam> feeValueAddedServiceNewAddParamList) ;

    /**
     * 根据条件修改
     *
     * @param feeValueAddedServiceNewQueryParam
     * @param feeValueAddedServiceNewAddParam
     * @return
     */
    boolean updateListByQueryParam(FeeValueAddedServiceNewQueryParam feeValueAddedServiceNewQueryParam, FeeValueAddedServiceNewAddParam feeValueAddedServiceNewAddParam) ;

    /**
     * 根据主键id删除
     *
     * @param id
     * @return
     */
    boolean removeById(Serializable id) ;

    /**
     * 根据主键id批量删除
     *
     * @param idList
     * @return
     */
    boolean removeByIds(List<Long> idList) ;

    /**
     * 根据条件删除
     *
     * @param feeValueAddedServiceNewQueryParam
     * @return
     */
    boolean removeByQueryParam(FeeValueAddedServiceNewQueryParam feeValueAddedServiceNewQueryParam) ;

}
