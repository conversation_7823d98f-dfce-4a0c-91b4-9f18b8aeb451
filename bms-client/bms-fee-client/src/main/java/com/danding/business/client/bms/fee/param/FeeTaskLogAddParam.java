package com.danding.business.client.bms.fee.param;

import com.danding.component.common.base.DO.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 计费进度日志表添加
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-05
 */

@Data
@ApiModel(value="FeeTaskLog对象", description="计费进度日志表添加")
public class FeeTaskLogAddParam  extends BaseEntity  implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 计费ID
     */
    @ApiModelProperty(value = "计费ID")
    private Long contractFeeId;

    /**
     * 起始日期
     */
    @ApiModelProperty(value = "起始日期")
    private String startTaskDate;

    @ApiModelProperty(value = "")
    private String midTaskDate;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    private String endTaskDate;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;

    /**
     * 次数
     */
    @ApiModelProperty(value = "次数")
    private Integer num;

    /**
     * 方法名称
     */
    @ApiModelProperty(value = "方法名称")
    private String methodName;

    /**
     * 结果
     */
    @ApiModelProperty(value = "结果")
    private Integer result;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sortno;


}
