package com.danding.business.client.bms.fee.param;

import com.danding.component.common.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 仓储费设置主表查询
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-17
 */

@Data
@ApiModel(value="FeeStorageTally对象", description="仓储费设置主表查询")
public class FeeStorageTallyQueryParam extends Page {


    private static final long serialVersionUID = 1L;

    /**
    * id
    */
    private Long id;


    /**
     * 计费ID
     */
    @ApiModelProperty(value = "计费ID")
    private String contractFeeId;

    /**
     * 计费方式
     */
    @ApiModelProperty(value = "计费方式")
    private String feeType;

    /**
     * 报价单位
     */
    @ApiModelProperty(value = "报价单位")
    private String feeUnit;

    /**
     * 价格设置类型
     */
    @ApiModelProperty(value = "价格设置类型")
    private String priceSetType;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;


}
