package com.danding.business.clien.bms.rpc.contract.result;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class RpcContractResult implements Serializable {

    private Long id;

    private Long createTime;

    private Long updateTime;

    private Long createBy;

    private Long updateBy;

    private Long version;

    private Integer deleted;

    /*
     * 计费系统编码
     */
    @ApiModelProperty(value = "计费系统编码")
    private String systemCode;

    /*
     * 计费系统类别
     */
    @ApiModelProperty(value = "计费系统类别")
    private String systemClass;

    /**
     * 公司别名
     */
    @ApiModelProperty(value = "公司别名")
    private String signedCompanyEnd;

    /*
     * 审核拒绝原因
     */
    @ApiModelProperty(value = "审核拒绝原因")
    private String reason;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    /**
     * 仓库名称
     */
    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    /**
     * 费用项目名称
     */
    @ApiModelProperty(value = "费用项目名称")
    private String contractName;

    /**
     * 费用项目编码,唯一，不允许修改。
     */
    @ApiModelProperty(value = "费用项目编码,唯一，不允许修改。")
    private String contractCode;

    @ApiModelProperty(value = "费用项目版本")
    private String contractVer;

    /**
     * 签约公司
     */
    @ApiModelProperty(value = "签约公司")
    private String signedCompany;

    /**
     * 商品类目:多条记录用分隔符号,隔开
     */
    @ApiModelProperty(value = "商品类目:多条记录用分隔符号,隔开")
    private String productClass;

    /**
     * 口岸
     */
    @ApiModelProperty(value = "口岸")
    private String warehousePort;

    /**
     * 计费ID
     */
    @ApiModelProperty(value = "计费ID")
    private String contractFeeId;



    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    /**
     * 货主名称
     */
    @ApiModelProperty(value = "货主名称")
    private String cargoName;

    /**
     * 合同签约时间
     */
    @ApiModelProperty(value = "合同签约时间")
    private Long signedTime;

    /**
     * 生效开始时间
     */
    @ApiModelProperty(value = "生效开始时间")
    private Long validityStartTime;

    /**
     * 生效结束时间
     */
    @ApiModelProperty(value = "生效结束时间")
    private Long validityEndTime;

    /**
     * 合同签约开始时间
     */
    @ApiModelProperty(value = "合同签约开始时间")
    private Long contractStartTime;

    /**
     * 合同签约结束时间
     */
    @ApiModelProperty(value = "合同签约结束时间")
    private Long contractEndTime;

    /**
     * 合同附件
     */
    @ApiModelProperty(value = "合同附件")
    private String contractFile;

    /**
     * 审核状态：具体值取自字典表，带上自己的模块号去查（模块号在字典表里自定义）
     * 待审核：1，审核驳回：3，审核通过：2，待生效：4，生效中：0，作废：5，待编辑：6，已停用：7，待启用：8
     */
    @ApiModelProperty(value = "审核状态：具体值取自字典表，带上自己的模块号去查（模块号在字典表里自定义）")
    private String status;

    /**
     * 临时状态标识
     */
    @ApiModelProperty(value = "1表示临时数据")
    private Integer flag;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称")
    private String contractFileName;

    /**
     * 贸易类型
     */
    @ApiModelProperty(value = "贸易类型")
    private String tradeType;

    /**
     * 类目编码
     */
    @ApiModelProperty(value = "类目编码")
    private List<String> productClassCode;

    /**
     * 类目
     */
    @ApiModelProperty(value = "类目")
    private List<String> productClassList;

    private Long userId;

    /**
     * ERP主账户
     */
    @ApiModelProperty(value = "ERP主账户")
    private String erpUser;

    /**
     * 时间区间
     */
    @ApiModelProperty(value = "时间区间")
    private String dateTime;

    /**
     * 修改人
     */
    @ApiModelProperty(value = "时间区间")
    private String updateByName;

    /**
     * 商品数量
     */
    @ApiModelProperty(value = "商品数量")
    private String skuSize;

    /**
     * 审核人
     */
    @ApiModelProperty(value = "审核人")
    private String verifier;

    /**
     * 审核时间
     */
    @ApiModelProperty(value = "审核时间")
    private String verifierTime;

    /**
     * 提交人
     */
    @ApiModelProperty(value = "提交人")
    private String submit;

    /**
     * 提交时间
     */
    @ApiModelProperty(value = "提交时间")
    private String submitTime;

    private String email;

    private String billingCompany;
}
