package com.danding.business.portal.bms.admin.inflow.response;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class AdminPayExpressFeeResponse {
    /**
     * 承运商编码
     */
    private String carrierCode;
    /**
     * 承运商名称
     */
    private String carrierName;
    /**
     * 承运商快递费用
     */
    private BigDecimal carrierFee;
    /**
     * 承运商快递单数量
     */
    private Integer carrierCount;
}
