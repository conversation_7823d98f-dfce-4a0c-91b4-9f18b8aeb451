package com.danding.business.portal.bms.admin.fee.Interface;


import com.danding.business.client.bms.fee.param.FeeTotalDetailQueryParam;
import com.danding.business.portal.bms.admin.fee.form.*;
import com.danding.business.portal.bms.admin.fee.response.newFeeTotalDetail.AdminFeeTotalDetailNewResponse;
import com.danding.business.portal.bms.admin.fee.response.adminFeeTotalResponse;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.soul.client.common.result.RpcResult;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <p>
 * 月账单汇总详情 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-06
 */
public interface IAdminFeeTotalDetailNewInterface {

    RpcResult getAllCalType(String tradeType);

    RpcResult<String> updateCompanyCargoStatus(adminFeeTotalDetailQueryForm queryForm);

    RpcResult invoicing(adminFeeTotalDetailAddForm addForm);

    RpcResult getInvoicingType();

    RpcResult confirmPayment(adminFeeTotalDetailAddForm addForm);

    RpcResult reCal(@RequestBody AdminFeeTotalDetailNewQueryForm queryForm);

    RpcResult reCalCallBack(List<AdminFeeTotalDetailNewCallBack> callBackList);

    RpcResult<ListVO<adminFeeTotalResponse>> searchByParam(adminFeeTotalQueryForm queryForm);

    RpcResult<ListVO<AdminFeeTotalDetailNewResponse>> selectPage(AdminFeeTotalDetailNewQueryForm queryForm);
}
