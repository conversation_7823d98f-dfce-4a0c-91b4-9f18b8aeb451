package com.danding.business.portal.bms.admin.contract.controller;

import com.danding.business.client.bms.contract.facade.IContractFacade;
import com.danding.business.client.bms.contract.param.ContractQueryParam;
import com.danding.business.client.bms.contract.result.ContractResult;
import com.danding.business.common.bms.utils.DateUtil;
import com.danding.business.portal.bms.admin.contract.form.adminContractQueryForm;
import com.danding.business.portal.bms.admin.contract.response.adminContractResponse;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.park.helper.qyweixin.QyweixinHelper;
import com.danding.park.helper.qyweixin.client.webhook.param.TextMessage;
import com.danding.soul.client.common.result.RpcResult;
import org.apache.dubbo.config.annotation.DubboReference;

import java.util.Date;


/**
 * <AUTHOR>
 * @date 2021/8/19 13:25
 */
public class MyThreadEnd extends Thread{

    private adminContractQueryForm queryForm;

    @DubboReference
    private IContractFacade contractFacade;

    public MyThreadEnd(adminContractQueryForm queryForm)
    {
        this.queryForm = queryForm;
    }

    public void run()
    {
        adminContractResponse result = null;
        Date now = null;
        while (true) {
            now = new Date();
            Date currentDayStartTime = DateUtil.getCurrentDayStartTime(now);
            if (now==currentDayStartTime||"1".equals(queryForm.getRemark())){
                String dateForLong = DateUtil.dateToString(now, "yyyy-MM-dd");
                System.out.println(dateForLong);
                Long timeMq = DateUtil.GetDateLong("yyyy-MM-dd",dateForLong);
                //最后一天
                String lastDayOfMonth = DateUtil.getLastDayOfMonth(dateForLong);
                Long beforeCurrentDate = DateUtil.GetDateLong("yyyy-MM-dd",lastDayOfMonth);
                //最后三天时间
                Long dayTimeOne = new Long(86400000);
                long one = beforeCurrentDate - dayTimeOne;
                long two = one - dayTimeOne;
                //时间范围内推送
                if (timeMq>=two&&timeMq<=beforeCurrentDate||"1".equals(queryForm.getRemark())) {
                    ContractQueryParam queryParam = BeanUtils.copyProperties(queryForm, ContractQueryParam.class);
                    queryParam.setPageSize(10000);
                    queryParam.setCurrentPage(1);
                    ListVO<ContractResult> listVO = contractFacade.workBench(queryParam);
                    result = new adminContractResponse();
                    if (listVO!=null&&listVO.getDataList()!=null&&listVO.getDataList().size()>0){
                        result.setRemark(listVO.getDataList().size()+"");
                        result.setStatus("Bms自动推送");
                    }
                    if("1".equals(queryForm.getRemark())){
                        result.setStatus("Bms手动推送");
                        queryForm.setRemark("0");
                    }
                }
                if(result!=null){
                    Thread thread = new MyThread(result);
                    thread.start();
                    try {
                        Thread.sleep(dayTimeOne);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }
}
