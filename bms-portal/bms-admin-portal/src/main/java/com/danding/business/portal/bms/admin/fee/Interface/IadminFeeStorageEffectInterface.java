package com.danding.business.portal.bms.admin.fee.Interface;

import com.danding.business.portal.bms.admin.fee.form.adminFeeStorageEffectAddForm;
import com.danding.business.portal.bms.admin.fee.form.adminFeeStorageEffectQueryForm;
import com.danding.soul.client.common.result.RpcResult;

/**
 * <p>
 * 坪效表/单票入库理货件数与价格表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-18
 */
public interface IadminFeeStorageEffectInterface {
  RpcResult selectPage(adminFeeStorageEffectQueryForm queryForm);
  RpcResult feestorageeffectList(adminFeeStorageEffectQueryForm queryForm);
  RpcResult edit(adminFeeStorageEffectAddForm addForm);
  RpcResult detail(String id);
  RpcResult drop(String id);
}
