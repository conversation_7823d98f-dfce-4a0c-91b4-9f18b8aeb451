package com.danding.business.portal.bms.admin.data.Interface;

import com.danding.business.common.bms.vo.SelectOptionVO;
import com.danding.business.portal.bms.admin.data.form.adminDataSourceAddForm;
import com.danding.business.portal.bms.admin.data.form.adminDataSourceListForm;
import com.danding.business.portal.bms.admin.data.form.adminDataSourceQueryForm;
import com.danding.business.portal.bms.admin.dict.form.adminDictListForm;
import com.danding.soul.client.common.result.RpcResult;

import java.util.List;

/**
 * <p>
 * 数据来源表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-05
 */
public interface IadminDataSourceInterface {
  RpcResult selectPage(adminDataSourceQueryForm queryForm);
  RpcResult datasourceList(adminDataSourceQueryForm queryForm);
  RpcResult edit(adminDataSourceAddForm addForm);
  RpcResult detail(String id);
  RpcResult drop(String id);
  RpcResult<List<SelectOptionVO<String>>> listDatasourceByKey() ;
  RpcResult<List<SelectOptionVO<String>>> listAllDatasourceForSelect() ;
}
