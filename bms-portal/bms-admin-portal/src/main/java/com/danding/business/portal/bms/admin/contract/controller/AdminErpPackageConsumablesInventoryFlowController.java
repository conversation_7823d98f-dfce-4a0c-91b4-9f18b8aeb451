package com.danding.business.portal.bms.admin.contract.controller;


import com.danding.business.client.bms.contract.facade.IErpPackageConsumablesInventoryFlowFacade;
import com.danding.business.client.bms.contract.facade.IFeePackageConsumablesFacade;
import com.danding.business.client.bms.contract.param.ErpPackageConsumablesInventoryFlowQueryParam;
import com.danding.business.client.bms.contract.param.FeePackageConsumablesQueryParam;
import com.danding.business.client.bms.contract.result.ErpPackageConsumablesInventoryFlowResult;
import com.danding.business.portal.bms.admin.contract.form.AdminErpPackageConsumablesInventoryFlowQueryForm;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.soul.client.common.result.RpcResult;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 包耗材库存流水 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-16
 */
@RestController
@RequestMapping("/contract/ErpPackageConsum")
public class AdminErpPackageConsumablesInventoryFlowController {
    @DubboReference
    private IErpPackageConsumablesInventoryFlowFacade erpPackageConsumablesInventoryFlowFacade;

    @PostMapping(value ="/selectList")
    public RpcResult test(@RequestBody ErpPackageConsumablesInventoryFlowQueryParam erpPackageConsumablesInventoryFlowQueryForm) {
        ListVO<ErpPackageConsumablesInventoryFlowResult> listVO=this.erpPackageConsumablesInventoryFlowFacade.selectAllByWarehouseCodeAndOperationTime(erpPackageConsumablesInventoryFlowQueryForm);
        return RpcResult.success(listVO);

    }
}
