package com.danding.business.portal.bms.admin.dict.controller;


import cn.hutool.core.util.ObjectUtil;
import com.danding.business.client.bms.dict.enums.WarehousePartnersType;
import com.danding.business.client.bms.dict.facade.IDictWarehousePartnersFacade;
import com.danding.business.client.bms.dict.param.DictWarehousePartnersAddParam;
import com.danding.business.client.bms.dict.param.DictWarehousePartnersQueryParam;
import com.danding.business.client.bms.dict.result.DictWarehousePartnersResult;
import com.danding.business.portal.bms.admin.dict.form.AdminDictWarehousePartnersAddForm;
import com.danding.business.portal.bms.admin.dict.form.AdminDictWarehousePartnersQueryForm;
import com.danding.business.portal.bms.admin.dict.response.AdminDictWarehousePartnersResponse;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.component.common.utils.EnumUtils;
import com.danding.component.uc.helper.SimpleUserHelper;
import com.danding.soul.client.common.result.RpcResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 仓库合作伙伴 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-05
 */
@Api(tags = "仓库合作伙伴")
@RestController
@RequestMapping("/dictWarehousePartners")
public class AdminDictWarehousePartnersController {

    @DubboReference
    private IDictWarehousePartnersFacade dictWarehousePartnersFacade;

    @ApiOperation(value = "列表分页查询")
    @GetMapping("/pageQueryList")
    public RpcResult pageQueryList(AdminDictWarehousePartnersQueryForm queryForm) {
        DictWarehousePartnersQueryParam queryParam = BeanUtils.copyProperties(queryForm, DictWarehousePartnersQueryParam.class);
        ListVO<DictWarehousePartnersResult> resultListVO = dictWarehousePartnersFacade.pageListByQueryParam(queryParam);
        return RpcResult.success(ListVO.build(resultListVO.getPage(), BeanUtils.copyProperties(resultListVO.getDataList(), AdminDictWarehousePartnersResponse.class)));
    }

    @ApiOperation(value = "新增/编辑")
    @GetMapping("/save")
    public RpcResult save(AdminDictWarehousePartnersAddForm addForm) {
        DictWarehousePartnersAddParam addParam = BeanUtils.copyProperties(addForm, DictWarehousePartnersAddParam.class);
        if (StringUtils.isEmpty(addForm.getId())) {
            addParam.setStatus(1);
        } else {
            addParam.setId(Long.valueOf(addForm.getId()));
        }
        addParam.setDeleted(1);
        DictWarehousePartnersQueryParam queryCode = new DictWarehousePartnersQueryParam();
        queryCode.setDtWarehousePartnersCode(addForm.getDtWarehousePartnersCode());
        DictWarehousePartnersResult codeResult = dictWarehousePartnersFacade.getByQueryParam(queryCode);
        DictWarehousePartnersQueryParam queryName = new DictWarehousePartnersQueryParam();
        queryName.setDtWarehousePartnersName(addForm.getDtWarehousePartnersName());
        DictWarehousePartnersResult nameResult = dictWarehousePartnersFacade.getByQueryParam(queryName);

        if (ObjectUtil.isNotEmpty(codeResult)) {
            return RpcResult.error("仓库编码已经存在");
        } else if (ObjectUtil.isNotEmpty(nameResult)) {
            return RpcResult.error("仓库名称已经存在");
        }
        Long userId = SimpleUserHelper.getUserId();
        addParam.setUpdateBy(userId);
        return RpcResult.isSuccess(dictWarehousePartnersFacade.add(addParam), "创建失败。");
    }

    @ApiOperation(value = "仓库合作伙伴状态")
    @GetMapping("/warehousePartnersType")
    public RpcResult warehousePartnersType() {
        return RpcResult.success(EnumUtils.buildName(WarehousePartnersType.class));
    }

    @ApiOperation(value = "禁用仓库合作伙伴")
    @GetMapping("/prohibitWarehousePartners")
    public RpcResult prohibitWarehousePartners(AdminDictWarehousePartnersAddForm addForm) {
        DictWarehousePartnersAddParam addParam = BeanUtils.copyProperties(addForm, DictWarehousePartnersAddParam.class);
        DictWarehousePartnersResult result = dictWarehousePartnersFacade.getById(addForm.getId());

        if (ObjectUtil.isNotEmpty(result)) {
            addParam.setId(Long.valueOf(addForm.getId()));
        }
        if (ObjectUtil.isNotEmpty(result) && result.getStatus().equals(1)) {
            ///TODO:仓库成本配置中存在则无法禁用
            //////////////////////////////////
        }

        return dictWarehousePartnersFacade.add(addParam) ? RpcResult.success("提交成功") : RpcResult.error("提交失败");
    }

    @ApiOperation(value = "删除")
    @GetMapping("/drop")
    public RpcResult drop(AdminDictWarehousePartnersQueryForm queryForm) {
        DictWarehousePartnersQueryParam queryParam = BeanUtils.copyProperties(queryForm, DictWarehousePartnersQueryParam.class);
        DictWarehousePartnersResult result = dictWarehousePartnersFacade.getByQueryParam(queryParam);
        if (ObjectUtil.isNotEmpty(result)){
            if (result.getStatus().equals(0)){
                ///TODO:仓库成本配置中存在则无法删除
                //////////////////////////////////
                return dictWarehousePartnersFacade.removeByQueryParam(queryParam) ? RpcResult.success("删除成功") : RpcResult.error("删除失败");
            }
            else {
                return RpcResult.error("只有状态为【关闭】的合作伙伴可以删除");
            }
        }
        else {
            return RpcResult.error("查询不到该数据");
        }
    }

    @ApiOperation(value = "获取某种类型的合作伙伴列表")
    @GetMapping("/listPartnersByType")
    public RpcResult<List<DictWarehousePartnersResult>> listPartnersByType(AdminDictWarehousePartnersQueryForm queryForm) {
        if (StringUtils.isEmpty(queryForm.getDtWarehousePartnersType())) {
            return RpcResult.error("未提供类型！");
        }
        DictWarehousePartnersQueryParam queryParam = new DictWarehousePartnersQueryParam();
        queryParam.setDtWarehousePartnersType(queryParam.getDtWarehousePartnersType());
        queryParam.setStatus(1);
        return RpcResult.success(dictWarehousePartnersFacade.listByQueryParam(queryParam));
    }
}
