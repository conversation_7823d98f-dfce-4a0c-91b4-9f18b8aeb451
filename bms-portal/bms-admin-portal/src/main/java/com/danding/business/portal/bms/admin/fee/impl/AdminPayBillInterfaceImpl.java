package com.danding.business.portal.bms.admin.fee.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.danding.business.client.bms.contract.facade.IContractFacade;
import com.danding.business.client.bms.contract.param.ContractQueryParam;
import com.danding.business.client.bms.contract.result.ContractResult;
import com.danding.business.client.bms.data.facade.IDataAreaFacade;
import com.danding.business.client.bms.data.param.DataAreaQueryParam;
import com.danding.business.client.bms.data.result.DataAreaResult;
import com.danding.business.client.bms.fee.facade.IExpressBillLogFacade;
import com.danding.business.client.bms.fee.facade.IPayBillFacade;
import com.danding.business.client.bms.fee.param.ExpressBillLogAddParam;
import com.danding.business.client.bms.fee.param.PayBillAddParam;
import com.danding.business.client.bms.fee.param.PayBillQueryParam;
import com.danding.business.client.bms.fee.result.ExpressCostCheckingStatusVo;
import com.danding.business.client.bms.fee.result.ExpressCostCheckingVO;
import com.danding.business.client.bms.fee.result.PayBillResult;
import com.danding.business.client.bms.inflow.facade.IInflowWarehouseFacade;
import com.danding.business.client.bms.inflow.param.InflowWarehouseQueryParam;
import com.danding.business.client.bms.inflow.result.InflowWarehouseResult;
import com.danding.business.common.bms.emums.PayBillStatusEnum;
import com.danding.business.portal.bms.admin.fee.Interface.IAdminPayBillInterface;
import com.danding.business.portal.bms.admin.fee.enums.AdjustTypeEnums;
import com.danding.business.portal.bms.admin.fee.form.AdminPayBillAddForm;
import com.danding.business.portal.bms.admin.fee.form.AdminPayBillQueryForm;
import com.danding.business.portal.bms.admin.fee.response.AdminPayBillResponse;
import com.danding.business.portal.bms.admin.fee.response.ExpressCostCheckingExcel;
import com.danding.business.portal.bms.admin.fee.response.ExpressCostCountOutExcel;
import com.danding.business.portal.bms.admin.fee.response.ExpressCostCountSysExcel;
import com.danding.business.portal.bms.admin.utils.HttpUtil;
import com.danding.business.common.bms.vo.SelectOptionVO;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.api.common.response.PageResult;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.component.common.utils.DateUtils;
import com.danding.component.common.utils.EnumUtils;
import com.danding.component.oss.ossclient.download.handler.ExportHandler;
import com.danding.component.uc.helper.SimpleUserHelper;
import com.danding.park.client.core.export.dto.ExportTaskDTO;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.result.RpcResult;
import com.dt.tms.rpc.waybill.client.ITmsBranchClient;
import com.dt.tms.rpc.waybill.param.branch.RpcBranchListReqVO;
import com.dt.tms.rpc.waybill.result.branch.RpcBranchListResVO;
import com.dt.tms.tool.common.TmsResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;


/**
 * <p>
 * 成本支出月账单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-04
 */
@Slf4j
@RestController
@DubboService
public class AdminPayBillInterfaceImpl implements IAdminPayBillInterface {
    @Value("${dataCenterAddress:}")
    private String dataCenterAddress;
    @Autowired
    private ExportHandler exportHandler;

    @DubboReference
    private IPayBillFacade payBillFacade;

    @DubboReference
    private ITmsBranchClient tmsBranchClient;
    @DubboReference
    private IExpressBillLogFacade expressBillLogFacade;

    @DubboReference
    private IInflowWarehouseFacade inflowWarehouseFacade;

    @DubboReference
    private IDataAreaFacade dataAreaFacade;

    @DubboReference
    private IContractFacade contractFacade;

    private static final String url = "/bill/expressCostChecking/selectExpressCostCheckingPage";
    private static final String queryPackageDetail = "/bill/freeExpressCost/getPackageDetailByExpressNo";

    private static final String adjustUrl = "/bill/expressCostChecking/adjustBatch";

    private static final String queryListUrl = "/bill/expressCostChecking/queryExpressCostCheckingList";

    private static final String computeUrl = "/bill/expressCostChecking/recalculate";

    private static final String enumUrl = "/bill/expressCostChecking/selectStatus";

    private static final String getBillStatusUrl = "/bill/expressCostChecking/getBillAmountStatus";

    private static final String getIdsUrl = "/bill/expressCostChecking/getIds";

    private static final String getBillSysUrl = "/bill/expressCostChecking/expressCostCountSys";

    private static final String getBillOutUrl = "/bill/expressCostChecking/expressCostCountOut";

    private static final String getBillCountByStatusUrl = "/bill/expressCostChecking/countByStatus";

    private static final String deleteExpressCostCheckingByIdsUrl = "/bill/expressCostChecking/deleteExpressCostCheckingByIds";



    @Override
    @SoulClient(path = "/payBill/query", desc = "查询单条")
    public RpcResult query(AdminPayBillQueryForm queryForm) {
        // 网关层统一设置userId
        queryForm.setUserId(SimpleUserHelper.getUserId());
        PayBillQueryParam queryParam = BeanUtils.copyProperties(queryForm, PayBillQueryParam.class);
        PayBillResult result = payBillFacade.getByQueryParam(queryParam);
        return RpcResult.success(BeanUtils.copyProperties(result, AdminPayBillResponse.class));
    }

    @Override
    @PostMapping("/payBill/queryList")
    @SoulClient(path = "/payBill/queryList", desc = "列表查询")
    public RpcResult queryList(AdminPayBillQueryForm queryForm) {
        // 网关层统一设置userId
        PayBillQueryParam queryParam = BeanUtils.copyProperties(queryForm, PayBillQueryParam.class);
        ListVO<PayBillResult> resultListVO = payBillFacade.pageListByQueryParam(queryParam);
        List<AdminPayBillResponse> responses = BeanUtils.copyProperties(resultListVO.getDataList(), AdminPayBillResponse.class);
        if (CollectionUtil.isNotEmpty(responses)) {
            responses.forEach(r -> r.setStatusName(Optional.ofNullable(PayBillStatusEnum.getEnum(r.getStatus())).map(PayBillStatusEnum::getMessage).orElse("")));
        }
        return RpcResult.success(ListVO.build(resultListVO.getPage(), responses));
    }

    @Override
    @SoulClient(path = "/payBill/selectItem", desc = "下拉查询")
    public RpcResult selectItem(AdminPayBillQueryForm queryForm) {
        // 网关层统一设置userId
        queryForm.setUserId(SimpleUserHelper.getUserId());
        PayBillQueryParam queryParam = BeanUtils.copyProperties(queryForm, PayBillQueryParam.class);
        List<PayBillResult> resultList = payBillFacade.listByQueryParam(queryParam);
        return RpcResult.success(EnumUtils.build(resultList, "id", "saleOrderNo"));
    }

    @Override
    @SoulClient(path = "/payBill/create", desc = "创建")
    public RpcResult create(AdminPayBillAddForm addForm) {
        // 网关层统一设置userId
        PayBillAddParam addParam = BeanUtils.copyProperties(addForm, PayBillAddParam.class);
        return RpcResult.isSuccess(payBillFacade.add(addParam), "创建失败。");
    }


    @Override
    @SoulClient(path = "/payBill/edit", desc = "编辑保存")
    public RpcResult edit(AdminPayBillAddForm addForm) {
        // 网关层统一设置userId
        PayBillAddParam addParam = BeanUtils.copyProperties(addForm, PayBillAddParam.class);
        return RpcResult.isSuccess(payBillFacade.updateById(addParam), "编辑保存失败。");
    }

    @Override
    @SoulClient(path = "/payBill/drop", desc = "删除")
    public RpcResult drop(AdminPayBillQueryForm queryForm) {
        // 网关层统一设置userId
        queryForm.setUserId(SimpleUserHelper.getUserId());
        PayBillQueryParam queryParam = BeanUtils.copyProperties(queryForm, PayBillQueryParam.class);
        return RpcResult.isSuccess(payBillFacade.removeByQueryParam(queryParam), "删除失败。");
    }

    @Override
    @SoulClient(path = "/payBill/detail", desc = "详情")
    public RpcResult detail(AdminPayBillQueryForm queryForm) {
        // 网关层统一设置userId
        queryForm.setUserId(SimpleUserHelper.getUserId());
        PayBillQueryParam queryParam = BeanUtils.copyProperties(queryForm, PayBillQueryParam.class);
        PayBillResult result = payBillFacade.getByQueryParam(queryParam);
        return RpcResult.success(BeanUtils.copyProperties(result, AdminPayBillResponse.class));
    }

    @PostMapping("/payBill/listPayBillEnum")
    @SoulClient(path = "/payBill/listPayBillEnum", desc = "成本账单状态")
    @Override
    public RpcResult listPayBillEnum() {
        return RpcResult.success(EnumUtils.build(PayBillStatusEnum.class, "value", "message"));
    }

    @Override
    @PostMapping("/payBill/listBranch")
    @SoulClient(path = "/payBill/listBranch", desc = "网点下拉列表")
    public RpcResult<List<SelectOptionVO<String>>> listBranch() {
        RpcBranchListReqVO rpcBranchListReqVO = new RpcBranchListReqVO();
        TmsResult<List<RpcBranchListResVO>> brandList = tmsBranchClient.findBrandList(rpcBranchListReqVO);
        List<RpcBranchListResVO> data = brandList.getData();
        List<SelectOptionVO<String>> branchData = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(data)) {
            Map<String, List<RpcBranchListResVO>> branchMap = data.stream().collect(Collectors.groupingBy(RpcBranchListResVO::getBranchAliasCode));
            for (Map.Entry<String, List<RpcBranchListResVO>> entry : branchMap.entrySet()) {
                RpcBranchListResVO rpcBranchListResVO = entry.getValue().get(0);

                SelectOptionVO<String> selectOptionVO = new SelectOptionVO<>();

                selectOptionVO.setId(rpcBranchListResVO.getBranchAliasCode());
                selectOptionVO.setName(rpcBranchListResVO.getBranchAliasName());

                branchData.add(selectOptionVO);
            }
        }
        return RpcResult.success(branchData);
    }

    @Override
    @SoulClient(path = "/payBill/updateStatus", desc = "账单状态变更")
    @PostMapping("/payBill/updateStatus")
    public RpcResult<String> updateStatus(AdminPayBillAddForm addForm) {
        if (StringUtils.isEmpty(addForm.getBranchCode())) {
            return RpcResult.error("网点编码为空");
        }
        if (StringUtils.isEmpty(addForm.getMonth())) {
            return RpcResult.error("账单月份为空");
        }
        PayBillAddParam addParam = BeanUtils.copyProperties(addForm, PayBillAddParam.class);
        return RpcResult.isSuccess(payBillFacade.updateStatus(addParam), "编辑保存失败。");
    }

    @Override
    @SoulClient(path = "/payBill/batchUpdateStatus", desc = "账单状态变更")
    @PostMapping("/payBill/batchUpdateStatus")
    public RpcResult<String> batchUpdateStatus(@RequestBody List<AdminPayBillAddForm> addForms) {
        try {
            if (CollectionUtil.isEmpty(addForms)) {
                return RpcResult.error("参数集合为空");
            }
            for (AdminPayBillAddForm addForm : addForms) {
                if (StringUtils.isEmpty(addForm.getBranchCode())) {
                    return RpcResult.error("网点编码为空");
                }
                if (StringUtils.isEmpty(addForm.getMonth())) {
                    return RpcResult.error("账单月份为空");
                }
            }
            payBillFacade.batchUpdateStatus(BeanUtils.copyProperties(addForms, PayBillAddParam.class));
            return RpcResult.success("操作成功");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return RpcResult.error(e.getMessage());
        }
    }

    @PostMapping("/payBill/updateExpressBillLogStatus")
    public void updateExpressBillLogStatus(@RequestBody Map<String, String> map) {
        try {
            Long id = Long.valueOf(map.get("id"));
            String status = map.get("status");
            ExpressBillLogAddParam expressBillLogAddParam = new ExpressBillLogAddParam();
            expressBillLogAddParam.setId(id);
            expressBillLogAddParam.setStatus(status);
            expressBillLogFacade.updateById(expressBillLogAddParam);
        } catch (Exception e) {
            log.warn("包裹物流操作日志状态回调异常:{}", e.getMessage(), e);
        }
    }

    @Override
    @SoulClient(path = "/payBill/queryBillDetail", desc = "快递对账明细列表")
    @PostMapping("/payBill/queryBillDetail")
    public RpcResult<ListVO<ExpressCostCheckingVO>> queryBillDetail(@RequestBody AdminPayBillQueryForm queryForm) {
        Map<String, Object> formParam = new HashMap<>();
        formParam.put("expressBranch", queryForm.getBranchCode());
        formParam.put("month", queryForm.getMonth());
        formParam.put("expressNo", queryForm.getExpressNo());
        formParam.put("status", queryForm.getStatus());
        formParam.put("percentMin", queryForm.getPercentMin());
        formParam.put("percentMax", queryForm.getPercentMax());
        formParam.put("weightDifferenceMin", queryForm.getWeightDifferenceMin());
        formParam.put("weightDifferenceMax", queryForm.getWeightDifferenceMax());
        formParam.put("pageSize", String.valueOf(queryForm.getPageSize()));
        formParam.put("pageNum", String.valueOf(queryForm.getCurrentPage()));
        formParam.put("startTime", queryForm.getStartTime());
        formParam.put("endTime", queryForm.getEndTime());
        formParam.put("field", queryForm.getField());
        formParam.put("order", queryForm.getOrder());

        
        String returnStr = HttpUtil.doPost(dataCenterAddress + url, formParam, 120000, 120000);
        if (null != returnStr) {
            JSONObject jsonObject = JSON.parseObject(returnStr);
            Integer integer = (Integer) jsonObject.get("code");
            if (0 != integer) {
                return RpcResult.error(jsonObject.get("msg").toString());
            } else {
                JSONObject jsonDataObject = jsonObject.getJSONObject("data");
                JSONObject pageObject = jsonDataObject.getJSONObject("page");
                JSONArray dataListObject = jsonDataObject.getJSONArray("dataList");
                List<ExpressCostCheckingVO> list = JSONObject.parseArray(dataListObject.toJSONString(), ExpressCostCheckingVO.class);
                list = this.getAreaName(list);
                PageResult pageResult = JSON.parseObject(pageObject.toJSONString(), PageResult.class);
                pageResult.setCurrentPage((Integer) pageObject.get("page"));
                pageResult.setTotalCount((Integer) pageObject.get("total"));
                ListVO<ExpressCostCheckingVO> expressCostCheckingVOListVO = new ListVO<>();
                expressCostCheckingVOListVO.setDataList(list);
                expressCostCheckingVOListVO.setPage(pageResult);
                return RpcResult.success(expressCostCheckingVOListVO);
            }
        }
        return null;
    }

    @Override
    @SoulClient(path = "/payBill/getPackageDetailByExpressNo", desc = "根据运单号查询包裹详情")
    @PostMapping("/payBill/getPackageDetailByExpressNo")
    public RpcResult getPackageDetailByExpressNo(@RequestBody AdminPayBillQueryForm queryForm) {
        Map<String, Object> formParam = new HashMap<>();
        Map<String,Object> map = new HashMap<>();
        formParam.put("expressNo", queryForm.getExpressNo());
        
        String returnStr = HttpUtil.doPost(dataCenterAddress + queryPackageDetail, formParam, 120000, 120000);
        if (null != returnStr) {
            JSONObject jsonObject = JSON.parseObject(returnStr);
            Integer integer = (Integer) jsonObject.get("code");
            if (0 != integer) {
                return RpcResult.error(jsonObject.get("msg").toString());
            } else {
                JSONObject jsonDataObject = jsonObject.getJSONObject("data");
                map = JSONObject.parseObject(jsonDataObject.toJSONString(), Map.class);
            }
        }
        return RpcResult.success(map);
    }

    @Override
    @PostMapping("/payBill/adjustBatch")
    @SoulClient(path = "/payBill/adjustBatch", desc = "批量调整")
    public RpcResult adjustBatch(@RequestBody AdminPayBillQueryForm queryForm) {
        if (ObjectUtil.isEmpty(queryForm.getType())) {
            return RpcResult.error("调整方式为空");
        }
        Map<String, Object> formParam = new HashMap<>();
        formParam.put("ids", queryForm.getIds());
        if (StringUtils.isEmpty(queryForm.getIds())) {
            formParam.put("ids", getIds(queryForm));
        }

        PayBillQueryParam payBillQueryParam = new PayBillQueryParam();
        if (StringUtils.isEmpty(queryForm.getBranchCode())) {
            return RpcResult.error("网点编码为空");
        }
        if (StringUtils.isEmpty(queryForm.getMonth())) {
            return RpcResult.error("调整月份为空");
        }
        payBillQueryParam.setBranchCode(queryForm.getBranchCode());
        payBillQueryParam.setMonth(queryForm.getMonth());
        PayBillResult payBillResult = this.payBillFacade.getByQueryParam(payBillQueryParam);
        if (payBillResult != null) {
            if (payBillResult.getStatus() > 2) {
                return RpcResult.error("已对账完成，不可调整");
            }
        } else {
            return RpcResult.error("账单不存在，不可调整");
        }

        String userName = SimpleUserHelper.getUserName();
        formParam.put("type", queryForm.getType());
        formParam.put("remark", queryForm.getRemark());
        formParam.put("expressBranch", queryForm.getBranchCode());
        formParam.put("month", queryForm.getMonth());
        
        String returnStr = HttpUtil.doPost(dataCenterAddress + adjustUrl, formParam, 120000, 120000);
        if (null != returnStr) {
            JSONObject jsonObject = JSON.parseObject(returnStr);
            Integer integer = (Integer) jsonObject.get("code");
            if (0 != integer) {
                return RpcResult.error(jsonObject.get("msg").toString());
            } else {
                String ids = formParam.get("ids").toString();
                List<String> idList = Arrays.asList(ids.split(","));
                //每次取1000条数据异步执行下面方法
                int size = 1000;
                int count = idList.size() / size;
                if (idList.size() % size > 0) {
                    count++;
                }
                for (int i = 0; i < count; i++) {
                    int fromIndex = i * size;
                    int toIndex = (i + 1) * size;
                    if (toIndex > idList.size()) {
                        toIndex = idList.size();
                    }
                    List<String> subList = idList.subList(fromIndex, toIndex);
                    String idsStr = StringUtils.join(subList, ",");
                    Map<String, Object> formParam1 = new HashMap<>();
                    formParam1.put("ids", idsStr);
                    formParam1.put("type", queryForm.getType());
                    formParam1.put("remark", queryForm.getRemark());
                    //异步调用addExpressBillLog方法
                    CompletableFuture.runAsync(() -> {
                        try {
                            //获取更新的数据
                            List<ExpressCostCheckingVO> list = new ArrayList<>();
                            String expressStr = HttpUtil.doPost(dataCenterAddress + queryListUrl, formParam1, 120000, 120000);
                            if (null != expressStr) {
                                JSONObject jsonObjectExpressStr = JSON.parseObject(expressStr);
                                Integer code = (Integer) jsonObjectExpressStr.get("code");
                                if (0 == code) {
                                    JSONArray dataListObject = jsonObjectExpressStr.getJSONArray("data");
                                    list = JSONObject.parseArray(dataListObject.toJSONString(), ExpressCostCheckingVO.class);
                                }
                            }
                            if (CollectionUtils.isNotEmpty(list)) {
                                this.payBillFacade.addExpressBillLog(list, idsStr, queryForm.getType(), userName, queryForm.getRemark(), queryForm.getAttachment());
                            }
                        } catch (Exception e) {
                            log.warn(e.getMessage(), e);
                        }
                    });
                }
                return RpcResult.success("操作成功");
            }
        } else {
            return RpcResult.error("操作失败");
        }
    }


    public String getIds(AdminPayBillQueryForm queryForm) {
        Map<String, Object> formParam = new HashMap<>();
        formParam.put("expressBranch", queryForm.getBranchCode());
        formParam.put("month", queryForm.getMonth());
        formParam.put("expressNo", queryForm.getExpressNo());
        formParam.put("status", queryForm.getStatus());
        formParam.put("percentMin", queryForm.getPercentMin());
        formParam.put("percentMax", queryForm.getPercentMax());
        String ids = null;
        
        String returnStr = HttpUtil.doPost(dataCenterAddress + getIdsUrl, formParam, 120000, 120000);
        if (null != returnStr) {
            JSONObject jsonObject = JSON.parseObject(returnStr);
            Integer integer = (Integer) jsonObject.get("code");
            if (0 != integer) {
                return null;
            } else {
                ids = jsonObject.getObject("data", String.class);
            }
        }
        return ids;
    }

    @Override
    @PostMapping("/payBill/recompute")
    @SoulClient(path = "/payBill/recompute", desc = "重新计算")
    public RpcResult recompute(@RequestBody AdminPayBillAddForm addForm) {
        try {
            if (StringUtils.isEmpty(addForm.getBranchCode())) {
                return RpcResult.error("网点编码为空");
            }
            if (StringUtils.isEmpty(addForm.getMonth())) {
                return RpcResult.error("月份为空");
            }
            ContractQueryParam contractQueryParam = new ContractQueryParam();
            contractQueryParam.setWarehouseCode(addForm.getBranchCode());
            contractQueryParam.setHaveCost(true);
            contractQueryParam.setStatus("0");
            ContractResult contractResult = this.contractFacade.getContractByContractQueryParam(contractQueryParam);
            long updateTime = contractResult.getUpdateTime();
            long now = System.currentTimeMillis();
            if (now - updateTime < 300000) {
//                return RpcResult.error("配置未同步到数据中台，请等待5分钟后再重新计算!");
            }
            Map<String, Object> formParam = new HashMap<>();
            formParam.put("expressBranch", addForm.getBranchCode());
            formParam.put("month", addForm.getMonth());
            
            //修改账单状态
            PayBillQueryParam payBillQueryParam = new PayBillQueryParam();
            payBillQueryParam.setBranchCode(addForm.getBranchCode());
            payBillQueryParam.setMonth(addForm.getMonth());
            PayBillResult payBillResult = payBillFacade.getByQueryParam(payBillQueryParam);
            if (payBillResult != null) {
                if (payBillResult.getStatus() > 2) {
                    return RpcResult.error("已对账完成，重新计算!");
                }
                PayBillAddParam payBillAddParam = BeanUtils.copyProperties(payBillResult, PayBillAddParam.class);
                payBillAddParam.setTaskStatus(1);
                payBillFacade.updateById(payBillAddParam);
                CompletableFuture.runAsync(() -> HttpUtil.doPost(dataCenterAddress + computeUrl, formParam, 120000, 120000));
                return RpcResult.success("操作成功");
            } else {
                return RpcResult.error("账单不存在");
            }
        } catch (Exception e) {
            log.error("成本账单重新计算异常：{}", e.getMessage(), e);
            return RpcResult.error(e.getMessage());
        }
    }

    @Override
    @PostMapping("/payBill/selectStatus")
    @SoulClient(path = "/payBill/selectStatus", desc = "账单明细枚举")
    public RpcResult selectStatus() {
        
        String returnStr = HttpUtil.doPost(dataCenterAddress + enumUrl, 120000, 120000);
        List<SelectOptionVO> selectOptionVOS = new ArrayList<>();
        if (StringUtils.isNotEmpty(returnStr)) {
            JSONObject jsonObject = JSON.parseObject(returnStr);
            Integer integer = (Integer) jsonObject.get("code");
            if (0 == integer) {
                JSONArray dataListObject = jsonObject.getJSONArray("data");
                selectOptionVOS = JSONObject.parseArray(dataListObject.toJSONString(), SelectOptionVO.class);
            }
        }
        return RpcResult.success(selectOptionVOS);
    }

    @PostMapping("/payBill/setStatus")
    @SoulClient(path = "/payBill/setStatus", desc = "对账完成操作")
    public RpcResult setStatus(@RequestBody AdminPayBillAddForm addForm) {
        if (addForm.getId() == null) {
            return RpcResult.error("请选择操作的数据行!");
        }
        PayBillResult payBillResult = this.payBillFacade.getById(addForm.getId());
        Map<String, Object> formParam = new HashMap<>();
        formParam.put("expressBranch", payBillResult.getBranchCode());
        formParam.put("month", payBillResult.getMonth());
        
        String returnStr = HttpUtil.doPost(dataCenterAddress + getBillStatusUrl, formParam, 120000, 120000);
        List<AdminPayBillResponse> billList = null;
        if (StringUtils.isNotEmpty(returnStr)) {
            JSONObject jsonObject = JSON.parseObject(returnStr);
            Integer integer = (Integer) jsonObject.get("code");
            if (0 == integer) {
                JSONArray dataListObject = jsonObject.getJSONArray("data");
                billList = JSONObject.parseArray(dataListObject.toJSONString(), AdminPayBillResponse.class);
            }
        }
        if (CollectionUtils.isNotEmpty(billList)) {
            AdminPayBillResponse bill = billList.get(0);
            if (bill != null && bill.getStatus() == 3) {
                PayBillAddParam payBillAddParam = new PayBillAddParam();
                payBillAddParam.setStatus(3);
                payBillAddParam.setId(addForm.getId());
                this.payBillFacade.updateById(payBillAddParam);
                //添加账单日志
                try {
                    ExpressBillLogAddParam expressBillLogAddParam = new ExpressBillLogAddParam();
                    expressBillLogAddParam.setBranchCode(payBillResult.getBranchCode());
                    expressBillLogAddParam.setMonth(payBillResult.getMonth());
                    expressBillLogAddParam.setOperate("对账完成");
                    expressBillLogAddParam.setUserName(SimpleUserHelper.getUserName());
                    expressBillLogAddParam.setStatus("对账完成");
                    expressBillLogAddParam.setType(1);
                    expressBillLogAddParam.setCreateTime(System.currentTimeMillis());
                    expressBillLogFacade.add(expressBillLogAddParam);
                } catch (Exception ex) {
                    log.warn("添加账单日志异常{}", ex.getMessage(), ex);
                }
            } else {
                return RpcResult.error("当前账单未全部对平，请继续对账!");
            }
        }
        return RpcResult.success();
    }

    @PostMapping("/payBill/export")
    @SoulClient(path = "/payBill/export", desc = "导出未平数据")
    public RpcResult export(@RequestBody AdminPayBillQueryForm queryForm) {
        String fileName = "快递成本未平数据" + DateUtils.formatDate(new Date(), "yyyyMMddHHmmss");
        ExportTaskDTO taskDTO = exportHandler.createTaskDTO(fileName, SimpleUserHelper.getUserId(), SimpleUserHelper.getRealUserId());
        List<ExpressCostCheckingVO> expressCostCheckingVOList = this.getExpressCostChecking(queryForm.getBranchCode(), queryForm.getMonth());
        expressCostCheckingVOList = this.getAreaName(expressCostCheckingVOList);
        try {
            List<ExpressCostCheckingExcel> expressCostCheckingExcelList = BeanUtils.copyProperties(expressCostCheckingVOList, ExpressCostCheckingExcel.class);
            for (ExpressCostCheckingExcel expressCostCheckingExcel : expressCostCheckingExcelList) {
                //将揽收时间戳转换为时间字符串
                if (expressCostCheckingExcel.getCollectionTime() != null && !expressCostCheckingExcel.getCollectionTime().equals("0")) {
                    String localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(expressCostCheckingExcel.getCollectionTime()), ZoneId.systemDefault()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    expressCostCheckingExcel.setCollectionTimeStr(localDateTime);
                }
                if (expressCostCheckingExcel.getCollectionTimeOut() != null && !expressCostCheckingExcel.getCollectionTimeOut().equals("0")) {
                    String localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(expressCostCheckingExcel.getCollectionTimeOut()), ZoneId.systemDefault()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    expressCostCheckingExcel.setCollectionTimeOutStr(localDateTime);
                }
            }
            exportHandler.exportFile("BMS_WEB", expressCostCheckingExcelList, fileName, 0, "未平数据", taskDTO);
        } catch (Exception e) {
            log.error("[exportOrderList] exception, cause={}", e.getMessage(), e);
        }
        return RpcResult.success(true);
    }

    /**
     * @param expressBranch
     * @param month
     * @return
     */
    public List<ExpressCostCheckingVO> getExpressCostChecking(String expressBranch, String month) {
        List<ExpressCostCheckingVO> list = null;
        Map<String, Object> formParam = new HashMap<>();
        formParam.put("expressBranch", expressBranch);
        formParam.put("month", month);
        formParam.put("statusStr", "2,3,4,5,6,7,8,9");
        formParam.put("pageSize", 100000);
        formParam.put("pageNum", 1);
        
        String returnStr = HttpUtil.doPost(dataCenterAddress + url, formParam, 120000, 120000);
        if (null != returnStr) {
            JSONObject jsonObject = JSON.parseObject(returnStr);
            Integer integer = (Integer) jsonObject.get("code");
            if (0 != integer) {
                return null;
            } else {
                JSONObject jsonDataObject = jsonObject.getJSONObject("data");
                JSONArray dataListObject = jsonDataObject.getJSONArray("dataList");
                list = JSONObject.parseArray(dataListObject.toJSONString(), ExpressCostCheckingVO.class);
            }
        }
        return list;
    }

    /**
     * 获取快递成本对账单系统数据
     *
     * @param month
     * @return
     */
    public List<ExpressCostCountSysExcel> expressCostCountSys(String month) {
        List<ExpressCostCountSysExcel> list = null;
        Map<String, Object> formParam = new HashMap<>();
        formParam.put("month", month);
        
        String returnStr = HttpUtil.doPost(dataCenterAddress + getBillSysUrl, formParam, 120000, 120000);
        if (null != returnStr) {
            JSONObject jsonObject = JSON.parseObject(returnStr);
            Integer integer = (Integer) jsonObject.get("code");
            if (0 != integer) {
                return null;
            } else {
                JSONArray dataListObject = jsonObject.getJSONArray("data");
                if (dataListObject != null && dataListObject.size() > 0) {
                    list = JSONObject.parseArray(dataListObject.toJSONString(), ExpressCostCountSysExcel.class);
                }
            }
        }
        return list;
    }

    /**
     * 获取快递成本对账单系统数据
     *
     * @param month
     * @return
     */
    public List<ExpressCostCountOutExcel> expressCostCountOut(String month) {
        List<ExpressCostCountOutExcel> list = null;
        Map<String, Object> formParam = new HashMap<>();
        formParam.put("month", month);
        
        String returnStr = HttpUtil.doPost(dataCenterAddress + getBillOutUrl, formParam, 120000, 120000);
        if (null != returnStr) {
            JSONObject jsonObject = JSON.parseObject(returnStr);
            Integer integer = (Integer) jsonObject.get("code");
            if (0 != integer) {
                return null;
            } else {
                JSONArray dataListObject = jsonObject.getJSONArray("data");
                if (dataListObject != null && dataListObject.size() > 0) {
                    list = JSONObject.parseArray(dataListObject.toJSONString(), ExpressCostCountOutExcel.class);
                }
            }
        }
        return list;
    }

    @PostMapping("/payBill/expressCostCountSysExport")
    @SoulClient(path = "/payBill/expressCostCountSysExport", desc = "快递成本内部账单汇总导出")
    public RpcResult expressCostCountSysExport(@RequestBody AdminPayBillQueryForm queryForm) {
        if (StringUtils.isBlank(queryForm.getMonth())) {
            return RpcResult.error("月份不能为空");
        }
        List<ExpressCostCountSysExcel> expressCostCountSysExcelList = this.expressCostCountSys(queryForm.getMonth());
        if (CollectionUtils.isEmpty(expressCostCountSysExcelList)) {
            return RpcResult.error("导出数据为空");
        }
        String fileName = "快递成本内部账单汇总" + DateUtils.formatDate(new Date(), "yyyyMMddHHmmss");
        ExportTaskDTO taskDTO = exportHandler.createTaskDTO(fileName, SimpleUserHelper.getUserId(), SimpleUserHelper.getRealUserId());
        try {
            InflowWarehouseQueryParam feeWarehouseCostQueryParam = new InflowWarehouseQueryParam();
            List<InflowWarehouseResult> warehouseResultList = this.inflowWarehouseFacade.listInflowWarehouseByParam(feeWarehouseCostQueryParam);
            for (ExpressCostCountSysExcel expressCostCountSysExcel : expressCostCountSysExcelList) {
                for (InflowWarehouseResult warehouseResult : warehouseResultList) {
                    if (expressCostCountSysExcel.getWarehouseCode().equals(warehouseResult.getWarehouseCode())) {
                        expressCostCountSysExcel.setWarehouseName(warehouseResult.getName());
                    }
                }
            }
            exportHandler.exportFile("BMS_WEB", expressCostCountSysExcelList, fileName, 0, "快递成本内部账单汇总", taskDTO);
        } catch (Exception e) {
            log.error("[expressCostCountSysExport] exception, cause={}", e.getMessage(), e);
        }
        return RpcResult.success(true);
    }

    @PostMapping("/payBill/expressCostCountOutExport")
    @SoulClient(path = "/payBill/expressCostCountOutExport", desc = "快递成本外部账单汇总导出")
    public RpcResult expressCostCountOutExport(@RequestBody AdminPayBillQueryForm queryForm) {
        if (StringUtils.isBlank(queryForm.getMonth())) {
            return RpcResult.error("月份不能为空");
        }
        List<ExpressCostCountOutExcel> expressCostCountOutExcelList = this.expressCostCountOut(queryForm.getMonth());
        if (CollectionUtils.isEmpty(expressCostCountOutExcelList)) {
            return RpcResult.error("导出数据为空");
        }
        String fileName = "快递成本外部账单汇总" + DateUtils.formatDate(new Date(), "yyyyMMddHHmmss");
        ExportTaskDTO taskDTO = exportHandler.createTaskDTO(fileName, SimpleUserHelper.getUserId(), SimpleUserHelper.getRealUserId());
        try {
            InflowWarehouseQueryParam feeWarehouseCostQueryParam = new InflowWarehouseQueryParam();
            List<InflowWarehouseResult> warehouseResultList = this.inflowWarehouseFacade.listInflowWarehouseByParam(feeWarehouseCostQueryParam);
            for (ExpressCostCountOutExcel expressCostCountOutExcel : expressCostCountOutExcelList) {
                for (InflowWarehouseResult warehouseResult : warehouseResultList) {
                    if (expressCostCountOutExcel.getWarehouseCode().equals(warehouseResult.getWarehouseCode())) {
                        expressCostCountOutExcel.setWarehouseName(warehouseResult.getName());
                    }
                }
            }
            exportHandler.exportFile("BMS_WEB", expressCostCountOutExcelList, fileName, 0, "快递成本外部账单汇总", taskDTO);
        } catch (Exception e) {
            log.error("[expressCostCountSysExport] exception, cause={}", e.getMessage(), e);
        }
        return RpcResult.success(true);
    }


    /**
     * 获取地区名称
     *
     * @param listVO
     * @return
     */
    public List<ExpressCostCheckingVO> getAreaName(List<ExpressCostCheckingVO> listVO) {
        try {
            if (CollectionUtils.isEmpty(listVO)) {
                return listVO;
            }
            DataAreaQueryParam dataAreaQueryParam = new DataAreaQueryParam();
            List<DataAreaResult> dataAreaResultList = this.dataAreaFacade.listDataAreaByParam(dataAreaQueryParam);
            InflowWarehouseQueryParam feeWarehouseCostQueryParam = new InflowWarehouseQueryParam();
            List<InflowWarehouseResult> warehouseResultList = this.inflowWarehouseFacade.listInflowWarehouseByParam(feeWarehouseCostQueryParam);
            for (ExpressCostCheckingVO expressCostCheckingVO : listVO) {
                if (CollectionUtils.isNotEmpty(warehouseResultList)) {
                    for (DataAreaResult dataAreaResult : dataAreaResultList) {
                        if (expressCostCheckingVO.getReceiverProv() != null && expressCostCheckingVO.getReceiverProv().equals(dataAreaResult.getAreaCode())) {
                            expressCostCheckingVO.setReceiverProvStr(dataAreaResult.getAreaName());
                        }
                        if (expressCostCheckingVO.getReceiverCity() != null && expressCostCheckingVO.getReceiverCity().equals(dataAreaResult.getAreaCode())) {
                            expressCostCheckingVO.setReceiverCityStr(dataAreaResult.getAreaName());
                        }
                        if (expressCostCheckingVO.getReceiverArea() != null && expressCostCheckingVO.getReceiverArea().equals(dataAreaResult.getAreaCode())) {
                            expressCostCheckingVO.setReceiverAreaStr(dataAreaResult.getAreaName());
                        }
                        if (expressCostCheckingVO.getReceiverProvOut() != null && expressCostCheckingVO.getReceiverProvOut().equals(dataAreaResult.getAreaCode())) {
                            expressCostCheckingVO.setReceiverProvOutStr(dataAreaResult.getAreaName());
                        }
                        if (expressCostCheckingVO.getReceiverCityOut() != null && expressCostCheckingVO.getReceiverCityOut().equals(dataAreaResult.getAreaCode())) {
                            expressCostCheckingVO.setReceiverCityOutStr(dataAreaResult.getAreaName());
                        }
                        if (expressCostCheckingVO.getReceiverAreaOut() != null && expressCostCheckingVO.getReceiverAreaOut().equals(dataAreaResult.getAreaCode())) {
                            expressCostCheckingVO.setReceiverAreaOutStr(dataAreaResult.getAreaName());
                        }
                        if (expressCostCheckingVO.getSysProv() != null && expressCostCheckingVO.getSysProv().equals(dataAreaResult.getAreaCode())) {
                            expressCostCheckingVO.setSysProvStr(dataAreaResult.getAreaName());
                        }
                        if (expressCostCheckingVO.getSysCity() != null && expressCostCheckingVO.getSysCity().equals(dataAreaResult.getAreaCode())) {
                            expressCostCheckingVO.setSysCityStr(dataAreaResult.getAreaName());
                        }
                        if (expressCostCheckingVO.getSysArea() != null && expressCostCheckingVO.getSysArea().equals(dataAreaResult.getAreaCode())) {
                            expressCostCheckingVO.setSysAreaStr(dataAreaResult.getAreaName());
                        }
                    }
                }
                //获取仓库名称
                if (CollectionUtils.isNotEmpty(warehouseResultList)) {
                    for (InflowWarehouseResult inflowWarehouseResult : warehouseResultList) {
                        if (expressCostCheckingVO.getWarehouseCode() != null && expressCostCheckingVO.getWarehouseCode().equals(inflowWarehouseResult.getWarehouseCode())) {
                            expressCostCheckingVO.setWarehouseName(inflowWarehouseResult.getName());
                        }
                    }
                    if (StringUtils.isEmpty(expressCostCheckingVO.getWarehouseName())) {
                        expressCostCheckingVO.setWarehouseName(expressCostCheckingVO.getWarehouseCode());
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取地区名称异常", e);
        }
        return listVO;
    }

    @Override
    @SoulClient(path = "/payBill/countByStatus", desc = "快递对账按状态分组统计")
    @PostMapping("/payBill/countByStatus")
    public RpcResult countByStatus(@RequestBody AdminPayBillQueryForm queryForm) {
        if (StringUtils.isEmpty(queryForm.getBranchCode())) {
            return RpcResult.error("网点编码不能为空!");
        }
        if (StringUtils.isEmpty(queryForm.getMonth())) {
            return RpcResult.error("月份不能为空!");
        }
        List<ExpressCostCheckingStatusVo> list = new ArrayList<>();
        Map<String, Object> formParam = new HashMap<>();
        formParam.put("expressBranch", queryForm.getBranchCode());
        formParam.put("month", queryForm.getMonth());
        formParam.put("expressNo", queryForm.getExpressNo());
        formParam.put("status", queryForm.getStatus());
        formParam.put("percentMin", queryForm.getPercentMin());
        formParam.put("percentMax", queryForm.getPercentMax());
        formParam.put("weightDifferenceMin", queryForm.getWeightDifferenceMin());
        formParam.put("weightDifferenceMax", queryForm.getWeightDifferenceMax());
        formParam.put("startTime", queryForm.getStartTime());
        formParam.put("endTime", queryForm.getEndTime());
        
        String returnStr = HttpUtil.doPost(dataCenterAddress + getBillCountByStatusUrl, formParam, 120000, 120000);
        if (null != returnStr) {
            JSONObject jsonObject = JSON.parseObject(returnStr);
            Integer integer = (Integer) jsonObject.get("code");
            if (0 != integer) {
                return null;
            } else {
                JSONArray dataListObject = jsonObject.getJSONArray("data");
                list = JSONObject.parseArray(dataListObject.toJSONString(), ExpressCostCheckingStatusVo.class);
            }
        }
        return RpcResult.success(list);
    }

    @Override
    @SoulClient(path = "/payBill/deleteExpressCostCheckingByIds", desc = "不计入成本计算，操作")
    @PostMapping("/payBill/deleteExpressCostCheckingByIds")
    public RpcResult deleteExpressCostCheckingByIds(@RequestBody AdminPayBillQueryForm queryForm) {
        if (queryForm.getId() == null) {
            return RpcResult.error("请选择操作的数据行!");
        }
        Map<String, Object> formParam = new HashMap<>();
        formParam.put("id", queryForm.getId());
        
        String returnStr = HttpUtil.doPost(dataCenterAddress + deleteExpressCostCheckingByIdsUrl, formParam, 120000, 120000);
        if (null != returnStr) {
            JSONObject jsonObject = JSON.parseObject(returnStr);
            Integer integer = (Integer) jsonObject.get("code");
            if (0 != integer) {
                return RpcResult.error(jsonObject.get("msg").toString());
            }
        }
        return RpcResult.success();
    }

    @Override
    @SoulClient(path = "/payBill/getAdjustType", desc = "获取调整类型")
    @PostMapping("/payBill/getAdjustType")
    public RpcResult getAdjustType() {
      List<Map<String,Object>>list=new ArrayList<>();
        for (AdjustTypeEnums adjustTypeEnums:AdjustTypeEnums.values()){
            Map<String,Object> map=new HashMap<>();
            map.put("type",adjustTypeEnums.getValue());
            map.put("name",adjustTypeEnums.getDesc());
            list.add(map);
        }
        return RpcResult.success(list);
    }

    /**
     * 手动创建账单
      */
    @Override
    @SoulClient(path = "/payBill/createPayBill", desc = "手动创建账单")
    @PostMapping("/payBill/createPayBill")
    public RpcResult createPayBill(@RequestBody AdminPayBillQueryForm queryForm) {
        try {
            PayBillQueryParam payBillQueryParam = new PayBillQueryParam();
            payBillQueryParam.setBranchCode(queryForm.getBranchCode());
            payBillQueryParam.setBranchName(queryForm.getBranchName());
            payBillQueryParam.setMonth(queryForm.getMonth());
            Boolean status=this.payBillFacade.createBill(payBillQueryParam);
            if (!status){
                return RpcResult.error("创建失败");
            }
        }catch (Exception e){
            log.error("手动创建账单异常{}",e.getMessage(),e);
            return RpcResult.error(e.getMessage());
        }
        return RpcResult.success();
    }
}
