package com.danding.business.portal.bms.admin.approve.controller;


import org.springframework.web.bind.annotation.RequestMapping;
import com.danding.business.client.bms.approve.facade.IApproveStorageFacade;
import com.danding.business.client.bms.approve.result.ApproveStorageResult;
import com.danding.business.client.bms.approve.param.ApproveStorageQueryParam;
import com.danding.business.client.bms.approve.param.ApproveStorageAddParam;
import com.danding.business.portal.bms.admin.approve.form.adminApproveStorageAddForm;
import com.danding.business.portal.bms.admin.approve.form.adminApproveStorageQueryForm;
import com.danding.business.portal.bms.admin.approve.response.adminApproveStorageResponse;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import java.util.List;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.danding.component.uc.helper.SimpleUserHelper;
import com.danding.soul.client.common.result.RpcResult;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.GetMapping;



import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 仓储费用 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-28
 */
@Api(tags = "仓储费用")
@RestController
@RequestMapping("/approveStorage")
public class adminApproveStorageController {
    @DubboReference
    private IApproveStorageFacade approvestorageFacade;

    @ApiOperation(value = "新增")
    @GetMapping("/save")
    public RpcResult edit(adminApproveStorageAddForm addForm) {
        // 用户端统一设置userId
        ApproveStorageAddParam addParam = BeanUtils.copyProperties(addForm, ApproveStorageAddParam.class);
        addParam.setDeleted(1);
        return approvestorageFacade.addApproveStorage(addParam) ? RpcResult.success("新增/编辑成功") : RpcResult.error("新增/编辑失败");
    }

    @ApiOperation(value = "查询详情")
    @GetMapping("/detail")
    public RpcResult detail(String id) {
        ApproveStorageQueryParam queryParam = new ApproveStorageQueryParam();
        queryParam.setId(Long.valueOf(id));
        List<ApproveStorageResult> results = approvestorageFacade.listApproveStorageByParam(queryParam);
             if (CollectionUtils.isEmpty(results)) {
             return RpcResult.error("查询不到该数据");
        }
        adminApproveStorageResponse response = BeanUtils.copyProperties(results.get(0), adminApproveStorageResponse.class);
        return RpcResult.success(response);
    }

     @ApiOperation(value = "删除")
     @GetMapping("/delete")
     public RpcResult drop(String id) {
         return approvestorageFacade.removeApproveStorageById(id) ? RpcResult.success("删除成功") : RpcResult.error("删除失败");
     }


     @ApiOperation(value = "列表")
     @GetMapping("/approvestorage/list")
     public RpcResult approvestorageList(adminApproveStorageQueryForm queryForm) {
         ApproveStorageQueryParam queryParam = BeanUtils.copyProperties(queryForm, ApproveStorageQueryParam.class);
         queryParam.setUserId(SimpleUserHelper.getUserId());
         List<ApproveStorageResult> results = approvestorageFacade.listApproveStorageByParam(queryParam);
         return RpcResult.success(results);
      }


      @ApiOperation(value = "分页查询")
      @GetMapping("/selectPage")
      public RpcResult selectPage(adminApproveStorageQueryForm queryForm) {
          ApproveStorageQueryParam queryParam = BeanUtils.copyProperties(queryForm, ApproveStorageQueryParam.class);
          ListVO<adminApproveStorageResponse> responseListVO = new ListVO<>();
          ListVO<ApproveStorageResult> listVO = approvestorageFacade.pageListApproveStorageByParam(queryParam);
          List<adminApproveStorageResponse> results = BeanUtils.copyProperties(listVO.getDataList(), adminApproveStorageResponse.class);
          responseListVO.setDataList(results);
          responseListVO.setPage(listVO.getPage());
          return RpcResult.success(responseListVO);
     }
}
