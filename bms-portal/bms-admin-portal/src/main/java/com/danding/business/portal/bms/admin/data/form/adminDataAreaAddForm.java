package com.danding.business.portal.bms.admin.data.form;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.danding.component.common.rpc.common.annotation.ResultReference;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 地区码表添加
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-13
 */

@Data
@ApiModel(value="DataArea对象", description="地区码表添加")
public class adminDataAreaAddForm implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
    * id
    */
    @ApiModelProperty(value = "id")
    @ResultReference(referenceType = ResultReference.ReferenceType.COPY, localReferProperty = "id")
    private String id;


    /**
     * 地区编码
     */
    @ApiModelProperty(value = "地区编码")
    private String areaCode;

    /**
     * 地区名
     */
    @ApiModelProperty(value = "地区名")
    private String areaName;

    /**
     * 地区级别（1:省份province,2:市city,3:区县district,4:街道street）
     */
    @ApiModelProperty(value = "地区级别（1:省份province,2:市city,3:区县district,4:街道street）")
    private Integer level;

    /**
     * 城市编码
     */
    @ApiModelProperty(value = "城市编码")
    private String cityCode;

    /**
     * 城市中心点（即：经纬度坐标）
     */
    @ApiModelProperty(value = "城市中心点（即：经纬度坐标）")
    private String center;

    /**
     * 地区父节点
     */
    @ApiModelProperty(value = "地区父节点")
    private Integer parentId;


}
