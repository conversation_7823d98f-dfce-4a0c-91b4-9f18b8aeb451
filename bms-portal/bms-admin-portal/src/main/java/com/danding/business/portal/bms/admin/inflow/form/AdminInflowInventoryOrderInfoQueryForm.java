package com.danding.business.portal.bms.admin.inflow.form;

import com.danding.component.common.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 清关单查询
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-09
 */

@Data
@ApiModel(value="InflowInventoryOrderInfo对象", description="清关单查询")
public class AdminInflowInventoryOrderInfoQueryForm extends Page {


    private static final long serialVersionUID = 1L;
    private Long id;
    /**
    * 创建时间
    */
    private Long createTime;
    /**
    * 更新时间
    */
    private Long updateTime;
    /**
    * 创建人
    */
    private Long createBy;
    /**
    * 更新人
    */
    private Long updateBy;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码")
    private String cargoCode;

    /**
     * 清关单号
     */
    @ApiModelProperty(value = "清关单号")
    private String inveCustomsSn;

    /**
     * 清关企业
     */
    @ApiModelProperty(value = "清关企业")
    private Long inveCompanyId;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String inveBusinessType;

    /**
     * 清单类型（指区港联动等）
     */
    @ApiModelProperty(value = "清单类型（指区港联动等）")
    private String customsInvtType;

    /**
     * 报关单号
     */
    @ApiModelProperty(value = "报关单号")
    private String customsEntryNo;

    /**
     * 报关企业
     */
    @ApiModelProperty(value = "报关企业")
    private Long customsEntryCompany;

    /**
     * 报关单类型
     */
    @ApiModelProperty(value = "报关单类型")
    private Integer customsEntryType;

    /**
     * 区间转出，区内转出的业务类型时[关联转入账册字段]
     */
    @ApiModelProperty(value = "区间转出，区内转出的业务类型时[关联转入账册字段]")
    private String inAccountBook;

    /**
     * 区间转入，区内转入的业务类型时[关联转出账册字段]
     */
    @ApiModelProperty(value = "区间转入，区内转入的业务类型时[关联转出账册字段]")
    private String outAccountBook;

    /**
     * 核放单编号
     */
    @ApiModelProperty(value = "核放单编号")
    private String refCheckOrderNo;

    /**
     * 核注清单编号
     */
    @ApiModelProperty(value = "核注清单编号")
    private String refHzInveNo;

    /**
     * 提取号
     */
    @ApiModelProperty(value = "提取号")
    private String pickUpNo;

    /**
     * 进出境关别
     */
    @ApiModelProperty(value = "进出境关别")
    private String entryExitCustoms;

    /**
     * 运输方式
     */
    @ApiModelProperty(value = "运输方式")
    private String transportMode;

    /**
     * 启运国
     */
    @ApiModelProperty(value = "启运国")
    private String shipmentCountry;

    /**
     * 租户
     */
    @ApiModelProperty(value = "租户")
    private String rentPerson;

    /**
     * 账册ID
     */
    @ApiModelProperty(value = "账册ID")
    private Long bookId;

    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人")
    private String applyPerson;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 清关单状态
     */
    @ApiModelProperty(value = "清关单状态")
    private String status;

    /**
     * 清关单状态对应时间
     */
    @ApiModelProperty(value = "清关单状态对应时间")
    private Date statusTime;

    /**
     * 状态:0.停用;1.启用(默认)
     */
    @ApiModelProperty(value = "状态:0.停用;1.启用(默认)")
    private Integer enable;

    /**
     * (真实同步数据删除状态)逻辑删除 -1 删除 1 
     */
    @ApiModelProperty(value = "(真实同步数据删除状态)逻辑删除 -1 删除 1 ")
    private Boolean realDeleted;


}
