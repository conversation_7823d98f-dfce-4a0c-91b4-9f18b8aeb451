package com.danding.business.portal.bms.admin.imports.form;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.danding.component.common.rpc.common.annotation.ResultReference;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 添加
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-18
 */

@Data
@ApiModel(value="Tally对象", description="添加")
public class adminTallyAddForm implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
    * id
    */
    @ApiModelProperty(value = "id")
    @ResultReference(referenceType = ResultReference.ReferenceType.COPY, localReferProperty = "id")
    private String id;


    /**
     * 仓库编码 取值仓库档案
     */
    @ApiModelProperty(value = "仓库编码 取值仓库档案")
    private String warehouseCode;

    /**
     * 货主编码 取值货主档案
     */
    @ApiModelProperty(value = "货主编码 取值货主档案")
    private String cargoCode;

    /**
     * 理货编号
     */
    @ApiModelProperty(value = "理货编号")
    private String tallyCode;

    /**
     * 理货单据
     */
    @ApiModelProperty(value = "理货单据")
    private String billNo;

    /**
     * 理货类型（参照ASN的type类型）
     */
    @ApiModelProperty(value = "理货类型（参照ASN的type类型）")
    private String type;

    /**
     * 理货人员
     */
    @ApiModelProperty(value = "理货人员")
    private String tallyBy;

    /**
     * 理货时间
     */
    @ApiModelProperty(value = "理货时间")
    private Long opDate;

    /**
     * 状态码
     */
    @ApiModelProperty(value = "状态码")
    private String status;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**
     * 创建时间 (时间戳)
     */
    @ApiModelProperty(value = "创建时间 (时间戳)")
    private Long createTime;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**
     * 更新时间 (时间戳)
     */
    @ApiModelProperty(value = "更新时间 (时间戳)")
    private Long updateTime;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 原因
     */
    @ApiModelProperty(value = "原因")
    private String reason;

    /**
     * ERP系统对应单号
     */
    @ApiModelProperty(value = "ERP系统对应单号")
    private String erpCode;


}
