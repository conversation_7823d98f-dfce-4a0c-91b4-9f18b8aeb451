package com.danding.business.portal.bms.admin;


import com.github.kevinsawicki.http.HttpRequest;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@RestController
@RequestMapping("/swagger")
public class YapiController {
    @Value("${yapi.token:default}")
    private String token;

    public YapiController() {
    }

    @GetMapping({"/yapi"})
    public String swagger(HttpServletRequest request) {
//        if (this.token.equals("default")) {
//            return "yapi token未配置";
//        } else {
            HttpRequest httpRequest = HttpRequest.get("http://localhost:" + request.getServerPort() + "/v2/api-docs");
            if (httpRequest.ok()) {
                Map map = Maps.newHashMap();
                map.put("type", "swagger");
                map.put("merge", "good");
                map.put("token", "6eeef11b4ab279265722bb2093f75f5bb47e813474fce2a6417d7282aefc06fe");
                map.put("json", httpRequest.body());
                httpRequest = HttpRequest.post("http://yapi.yang800.cn/api/open/import_data").form(map);
                if (httpRequest.ok()) {
                    return httpRequest.body();
                }
            }
            return "yapi请求失败";
//        }
    }


}