package com.danding.business.core.bms.inflow.entity;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.danding.business.common.bms.emums.CustomsClearanceBusinessType;
import com.danding.business.common.bms.emums.InflowBusinessType;
import com.danding.business.common.bms.vo.CCSInflowBaseEntity;
import com.danding.business.common.bms.vo.InflowBaseEntity;
import com.danding.component.common.base.DO.BaseEntity;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 清关核注核放单
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dt_inflow_customs_clearance_order_snapshot")
public class InflowCustomsClearanceOrderSnapshot extends CCSInflowBaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库编码
     */
    @TableId
    private String warehouseCode;

    /**
     * 货主编码
     */
    private String cargoCode;

    /**
     * 清关单号
     */
    private String customsClearanceNo;

    /**
     * 业务单号
     */
    private String businessNo;

    /**
     * 业务类型 HZ:核注 HF:核放
     */
    private CustomsClearanceBusinessType businessType;

    /**
     * 数量 
     */
    private Integer num;

    /**
     * 完成时间
     */
    private Date finishTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 单位 默认:单
     */
    private String unit;

    /**
     * (真实同步数据删除状态)逻辑删除 -1 删除 1 
     */
    private Boolean realDeleted;

    private InflowBusinessType inflowBusinessType;

    private BigDecimal price;

    private String endorsementNo;

    private String checklistNo;

    /**
     * 导入修改时间
     */
    private Long updatedTime;

    /**
     * 导入创建时间
     */
    private Long createdTime;

    /**
     * 计费版本
     */
    private Long feeVer;


}
