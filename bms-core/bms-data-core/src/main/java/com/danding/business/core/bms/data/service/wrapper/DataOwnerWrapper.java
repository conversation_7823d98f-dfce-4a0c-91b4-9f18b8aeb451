package com.danding.business.core.bms.data.service.wrapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import com.danding.business.core.bms.data.entity.DataOwner;

import com.danding.business.core.bms.data.search.DataOwnerSearch;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;


/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-28
 */
@Component
public class DataOwnerWrapper {

    public LambdaQueryWrapper<DataOwner> getQueryWrapper(DataOwnerSearch dataOwnerSearch){
        LambdaQueryWrapper<DataOwner> queryWrapper = new LambdaQueryWrapper<DataOwner>();
        queryWrapper.eq(!StringUtils.isEmpty(dataOwnerSearch.getCode()), DataOwner::getCode, dataOwnerSearch.getCode())
                .eq(!StringUtils.isEmpty(dataOwnerSearch.getWarehouseCode()), DataOwner::getWarehouseCode, dataOwnerSearch.getWarehouseCode())
                .like(!StringUtils.isEmpty(dataOwnerSearch.getName()), DataOwner::getName, dataOwnerSearch.getName())
                .eq(DataOwner::getDeleted, 1).orderByDesc(DataOwner::getId);
        return queryWrapper;
    }
}
