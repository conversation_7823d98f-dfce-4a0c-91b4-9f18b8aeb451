<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.business.core.bms.imports.mapper.MoveMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.danding.business.core.bms.imports.entity.Move">
        <result column="id" property="id" />
        <result column="version" property="version" />
        <result column="deleted" property="deleted" />
        <result column="warehouse_code" property="warehouseCode" />
        <result column="cargo_code" property="cargoCode" />
        <result column="code" property="code" />
        <result column="status" property="status" />
        <result column="exp_sku_type" property="expSkuType" />
        <result column="exp_sku_qty" property="expSkuQty" />
        <result column="actual_sku_type" property="actualSkuType" />
        <result column="actual_sku_qty" property="actualSkuQty" />
        <result column="op_type" property="opType" />
        <result column="op_by" property="opBy" />
        <result column="complete_date" property="completeDate" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="sku_quality" property="skuQuality" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        version,
        deleted,
        warehouse_code, cargo_code, code, status, exp_sku_type, exp_sku_qty, actual_sku_type, actual_sku_qty, op_type, op_by, complete_date, created_by, created_time, updated_by, updated_time, sku_quality
    </sql>

</mapper>
