package com.danding.business.core.bms.imports.search;

import com.danding.component.common.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 到货通知明细查询对象
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-16
 */

@Data
@ApiModel(value="AsnDetail对象", description="到货通知明细查询对象")
public class AsnDetailSearch extends Page {


    private static final long serialVersionUID = 1L;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码 取值仓库档案")
    private String warehouseCode;

    /**
     * 货主编码
     */
    @ApiModelProperty(value = "货主编码 取值货主档案")
    private String cargoCode;

    /**
     * 到货通知id
     */
    @ApiModelProperty(value = "到货通知id")
    private String asnId;

    /**
     * 上游行号
     */
    @ApiModelProperty(value = "上游行号")
    private String extNo;

    /**
     * 商品代码
     */
    @ApiModelProperty(value = "商品代码")
    private String skuCode;

    /**
     * 商品条码
     */
    @ApiModelProperty(value = "商品条码")
    private String upcCode;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String skuName;

    /**
     * 计划数量
     */
    @ApiModelProperty(value = "计划数量")
    private BigDecimal expSkuQty;

    /**
     * 实收数量
     */
    @ApiModelProperty(value = "实收数量")
    private BigDecimal recSkuQty;

    /**
     * 体积
     */
    @ApiModelProperty(value = "体积")
    private BigDecimal volume;

    /**
     * 毛重
     */
    @ApiModelProperty(value = "毛重")
    private BigDecimal grossWeight;

    /**
     * 净重
     */
    @ApiModelProperty(value = "净重")
    private BigDecimal netWeight;

    /**
     * 总价
     */
    @ApiModelProperty(value = "总价")
    private BigDecimal price;

    /**
     * 商品属性
     */
    @ApiModelProperty(value = "商品属性")
    private String skuQuality;

    /**
     * 外部批次编码
     */
    @ApiModelProperty(value = "外部批次编码")
    private String externalSkuLotNo;

    /**
     * 包装单位
     */
    @ApiModelProperty(value = "包装单位")
    private String packageUnitCode;

    /**
     * 状态码和ASN保持一致
     */
    @ApiModelProperty(value = "状态码和ASN保持一致")
    private String status;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createdBy;

    /**
     * 创建时间 (时间戳)
     */
    @ApiModelProperty(value = "创建时间 (时间戳)")
    private Long createdTime;

    /**
     * 更新时间 (时间戳)
     */
    @ApiModelProperty(value = "更新时间 (时间戳)")
    private Long updatedTime;
}
