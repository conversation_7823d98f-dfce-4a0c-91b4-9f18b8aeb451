package com.danding.business.core.bms.fee.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.danding.business.core.bms.fee.entity.FeeRepairSpoiledFood;
import com.danding.business.core.bms.fee.repository.FeeRepairSpoiledFoodRepository;
import com.danding.business.core.bms.fee.search.FeeRepairSpoiledFoodSearch;
import com.danding.business.core.bms.fee.service.IFeeRepairSpoiledFoodService;
import com.danding.business.core.bms.fee.service.wrapper.FeeRepairSpoiledFoodWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.io.Serializable;
import java.util.List;

/**
* <p>
* WMS-宠粮残次修复费 服务实现类
* </p>
*
* <AUTHOR>
* @since 2021-07-21
*/
@Service
public class FeeRepairSpoiledFoodServiceImpl implements IFeeRepairSpoiledFoodService {

    @Autowired
    private FeeRepairSpoiledFoodRepository feeRepairSpoiledFoodRepository;

    @Autowired
    private FeeRepairSpoiledFoodWrapper feeRepairSpoiledFoodWrapper;


    /**
    * 根据主键id查询
    *
    * @param id
    * @return
    */
    @Override
    public FeeRepairSpoiledFood selectById(Serializable id) {
        return feeRepairSpoiledFoodRepository.getById(id);
    }

    /**
    * 根据条件查询单个
    *
    * @param feeRepairSpoiledFoodSearch
    * @return
    */
    @Override
    public FeeRepairSpoiledFood selectBySearch(FeeRepairSpoiledFoodSearch feeRepairSpoiledFoodSearch) {
        return feeRepairSpoiledFoodRepository.getOne(feeRepairSpoiledFoodWrapper.getQueryWrapperSearch(feeRepairSpoiledFoodSearch));
    }

    /**
    * 根据条件查询
    *
    * @param feeRepairSpoiledFoodSearch
    * @return
    */
    @Override
    public List<FeeRepairSpoiledFood> selectListBySearch(FeeRepairSpoiledFoodSearch feeRepairSpoiledFoodSearch) {
        return feeRepairSpoiledFoodRepository.list(feeRepairSpoiledFoodWrapper.getQueryWrapperSearch(feeRepairSpoiledFoodSearch));
    }

    @Override
    public List<FeeRepairSpoiledFood> selectList(FeeRepairSpoiledFoodSearch feeRepairSpoiledFoodSearch) {
        return feeRepairSpoiledFoodRepository.list(feeRepairSpoiledFoodWrapper.getQueryWrapper(feeRepairSpoiledFoodSearch));
    }

    /**
    * 根据主键修改
    *
    * @param feeRepairSpoiledFood
    * @return
    */
    @Override
    public boolean updateById(FeeRepairSpoiledFood feeRepairSpoiledFood) {
        return feeRepairSpoiledFoodRepository.updateById(feeRepairSpoiledFood);
    }

    /**
    * 根据条件修改
    *
    * @param feeRepairSpoiledFoodSearch
    * @param feeRepairSpoiledFood
    * @return
    */
    @Override
    public boolean updateListBySearch(FeeRepairSpoiledFoodSearch feeRepairSpoiledFoodSearch, FeeRepairSpoiledFood feeRepairSpoiledFood) {
        return feeRepairSpoiledFoodRepository.update(feeRepairSpoiledFood, feeRepairSpoiledFoodWrapper.getQueryWrapper(feeRepairSpoiledFoodSearch));
    }

    /**
    * 主键id批量修改
    *
    * @param feeRepairSpoiledFoodList
    * @return
    */
    @Override
    public boolean updateListById(List<FeeRepairSpoiledFood> feeRepairSpoiledFoodList) {
        return feeRepairSpoiledFoodRepository.updateBatchById(feeRepairSpoiledFoodList);
    }

    /**
    * 单条插入
    *
    * @param feeRepairSpoiledFood
    * @return
    */
    @Override
    public boolean insert(FeeRepairSpoiledFood feeRepairSpoiledFood) {
        feeRepairSpoiledFood.setDeleted(1);
        if (ObjectUtils.isEmpty(feeRepairSpoiledFood.getLicenceNum())){
            feeRepairSpoiledFood.setLicenceNum(0);
            feeRepairSpoiledFood.setLicenceUnit("");
        }
        return feeRepairSpoiledFoodRepository.saveOrUpdate(feeRepairSpoiledFood);
    }

    /**
    * 批量插入
    *
    * @param feeRepairSpoiledFoodList
    * @return
    */
    @Override
    public boolean insertList(List<FeeRepairSpoiledFood> feeRepairSpoiledFoodList) {
    return feeRepairSpoiledFoodRepository.saveBatch(feeRepairSpoiledFoodList);
    }

    /**
    * 主键id删除
    *
    * @param id
    * @return
    */
    @Override
    public boolean deleteById(Serializable id) {
        return feeRepairSpoiledFoodRepository.removeById(id);
    }

    /**
    * 根据条件删除
    *
    * @param feeRepairSpoiledFoodSearch
    * @return
    */
    @Override
    public boolean deleteBySearch(FeeRepairSpoiledFoodSearch feeRepairSpoiledFoodSearch) {
        return feeRepairSpoiledFoodRepository.remove(feeRepairSpoiledFoodWrapper.getQueryWrapper(feeRepairSpoiledFoodSearch));
    }

    /**
    * 主键id批量删除
    *
    * @param idList
    * @return
    */
    @Override
    public boolean deleteByIds(List<Long> idList) {
        return feeRepairSpoiledFoodRepository.removeByIds(idList);
    }


}
