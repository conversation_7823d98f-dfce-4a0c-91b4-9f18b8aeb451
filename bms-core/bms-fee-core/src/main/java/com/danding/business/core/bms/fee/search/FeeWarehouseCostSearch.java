package com.danding.business.core.bms.fee.search;

import com.danding.component.common.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 仓维度成本查询对象
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-15
 */

@Data
@ApiModel(value="FeeWarehouseCost对象", description="仓维度成本查询对象")
public class FeeWarehouseCostSearch extends Page {


    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 仓库编码
     */
    @ApiModelProperty(value = "仓库编码")
    private String warehouseCode;

    /**
     * 成本类型
     */
    @ApiModelProperty(value = "成本类型")
    private String costType;

    /**
     * 账单月份
     */
    @ApiModelProperty(value = "账单月份")
    private String month;

    /**
     * 账单状态
     */
    @ApiModelProperty(value = "账单状态")
    private String status;
}
