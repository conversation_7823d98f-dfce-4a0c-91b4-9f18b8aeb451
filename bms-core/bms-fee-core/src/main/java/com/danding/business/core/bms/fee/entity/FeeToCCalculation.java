package com.danding.business.core.bms.fee.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.danding.component.common.base.DO.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 2C计费表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dt_fee_to_c_calculation")
public class FeeToCCalculation extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 计费规则ID
     */
    private Long feeRuleId;

    /**
     * 任务统一标识ID：由时间+任务ID+UserID
     */
    private String taskFeeMark;

    /**
     * 外键：2C费用表主键ID
     */
    private Long fkFeeTocId;

    /**
     * 费用类型
     */
    private Long fkFeeId;

    /**
     * 同步时间
     */
    private Long syncTime;

    /**
     * 计费时间
     */
    private Long billableTime;

    /**
     * 单项费用
     */
    private BigDecimal singleFee;

    /**
     * 单项数量
     */
    private String singleQty;

    /**
     * 计算是否成功
     */
    private Boolean result;

    /**
     * 日志内容
     */
    private String logContent;

    /**
     * 备注
     */
    private String remark;


}
