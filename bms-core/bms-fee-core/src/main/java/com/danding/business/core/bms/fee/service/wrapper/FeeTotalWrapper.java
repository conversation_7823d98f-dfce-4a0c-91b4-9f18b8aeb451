package com.danding.business.core.bms.fee.service.wrapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.danding.business.core.bms.fee.entity.FeeTotal;
import com.danding.business.core.bms.fee.search.FeeTotalSearch;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * <p>
 * 账单汇总 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-17
 */
@Component
public class FeeTotalWrapper {

    public LambdaQueryWrapper<FeeTotal> getQueryWrapper(FeeTotalSearch feeTotalSearch) {
        LambdaQueryWrapper<FeeTotal> queryWrapper = new LambdaQueryWrapper<FeeTotal>();
        queryWrapper.eq(feeTotalSearch.getContractFeeId() != "" && feeTotalSearch.getContractFeeId() != null, FeeTotal::getContractFeeId, feeTotalSearch.getContractFeeId())
                .eq(feeTotalSearch.getCompanyName() != "" && feeTotalSearch.getCompanyName() != null, FeeTotal::getCompanyName, feeTotalSearch.getCompanyName())
                .eq(feeTotalSearch.getCargoCode() != "" && feeTotalSearch.getCargoCode() != null, FeeTotal::getCargoCode, feeTotalSearch.getCargoCode())
                .eq(feeTotalSearch.getCargoName() != "" && feeTotalSearch.getCargoName() != null, FeeTotal::getCargoName, feeTotalSearch.getCargoName())
                .eq(feeTotalSearch.getWarehouseCode() != "" && feeTotalSearch.getWarehouseCode() != null, FeeTotal::getWarehouseCode, feeTotalSearch.getWarehouseCode())
                .eq(feeTotalSearch.getSignedCompanyEnd() != "" && feeTotalSearch.getSignedCompanyEnd() != null, FeeTotal::getSignedCompanyEnd, feeTotalSearch.getSignedCompanyEnd())
                .eq(feeTotalSearch.getWarehouseName() != null && feeTotalSearch.getWarehouseName() != "", FeeTotal::getWarehouseName, feeTotalSearch.getWarehouseName())
                .in(!CollectionUtils.isEmpty(feeTotalSearch.getIdList()), FeeTotal::getId, feeTotalSearch.getIdList())
                .eq(FeeTotal::getDeleted, 1).orderByDesc(FeeTotal::getUpdateTime);
        return queryWrapper;
    }

    public LambdaQueryWrapper<FeeTotal> pagSearchByParam(FeeTotalSearch feeTotalSearch) {
        LambdaQueryWrapper<FeeTotal> queryWrapper = new LambdaQueryWrapper<FeeTotal>();
        queryWrapper
                .eq(feeTotalSearch.getWarehouseCode() != null, FeeTotal::getWarehouseCode, feeTotalSearch.getWarehouseCode())
                .eq(FeeTotal::getDeleted, 1);
        if (StringUtils.isNotEmpty(feeTotalSearch.getContractFeeId()) || StringUtils.isNotEmpty(feeTotalSearch.getCargoName())
                || StringUtils.isNotEmpty(feeTotalSearch.getCargoCode()) || StringUtils.isNotEmpty(feeTotalSearch.getCompanyName())) {
            queryWrapper.and(
                    QueryWrapper -> {
                        QueryWrapper.like(StringUtils.isNotEmpty(feeTotalSearch.getContractFeeId()), FeeTotal::getContractFeeId, feeTotalSearch.getContractFeeId())
                                .or().like(StringUtils.isNotEmpty(feeTotalSearch.getCargoName()), FeeTotal::getCargoName, feeTotalSearch.getCargoName())
                                .or().like(StringUtils.isNotEmpty(feeTotalSearch.getCargoCode()), FeeTotal::getCargoCode, feeTotalSearch.getCargoCode())
                                .or().like(StringUtils.isNotEmpty(feeTotalSearch.getCompanyName()), FeeTotal::getCompanyName, feeTotalSearch.getCompanyName());
                    }
            );
        }
        queryWrapper.orderByDesc(FeeTotal::getUpdateTime).groupBy(FeeTotal::getCargoCode);
        return queryWrapper;
    }
}
