package com.danding.business.core.bms.contract.entity;

import com.danding.component.common.base.DO.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @date 2020/12/10 16:03
 * @Description
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class JobContractQuote  extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 费用项目名称
     */
    private String contractName;

    /**
     * 费用项目编码,唯一，不允许修改。
     */
    private String contractCode;

    /**
     * 签约公司
     */
    private String signedCompany;

    /**
     * 货主编码
     */
    private String cargoCode;


    /**
     * 合同签约时间
     */
    private Long signedTime;

    /**
     * 合同签约生效开始时间
     */
    private Long validityStartTime;

    /**
     * 合同签约生效结束时间
     */
    private Long validityEndTime;

    /**
     * 合同附件
     */
    private String contractFile;

    /**
     * 审核状态：具体值取自字典表，带上自己的模块号去查（模块号在字典表里自定义）
     */
    private Integer status;

    /**
     * 外键：取自费用项目主表主键ID
     */
    private Long fkFeeId;

    /**
     * 外键：合同表主键ID
     */
    private Long fkContractId;

    /**
     * 计费规则内容
     */
    private String feeRuleContent;

    /**
     * 计费规则类型:1 手动，2自动
     */
    private String feeRuleType;

    /**
     * 费用大类类型
     */
    private String fkFeeClassId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 费用编码
     */
    private String feeCode;

    /**
     * 仓储优惠方案
     */
    private String preferentialPlan;


    public String getDefaultContent(BigDecimal defaultCoefficient){
        String tuoRule = defaultCoefficient.toPlainString();
        String preContent = "[{\"zhujie\":\"单日单品折合平方米:feeResult,字典:hashMap,设置:storageSetResult," +
                "重量设置:listSWRSet,价格参数设置:listSPSet,坪效参数设置:listSERSet\",\"name\":\"1\"," +
                "\"description\":\"1\",\"priority\":1,\"compositeRuleType\":\"UnitRuleGroup\"," +
                "\"composingRules\":[{\"zhujie\":\"仓储费\",\"name\":\"仓储费-单日单品折合平方米 单条计算规则\"," +
                "\"description\":\"2\",\"condition\":\"1==1\",\"priority\":2,\"actions\"" +
                ":[\"totalZheHeArea=(Volume/1000000)*ShareFactor/";
        String lastContent = "*PhysicalQty; FeeDataRuleService.doCommonAction(feeResult,totalZheHeArea,1);" +
                "       \"]}]}]";
        return preContent + tuoRule + lastContent;
    }
}
